<?php

/**
 * DCIM Server Management Module
 * 
 * This file contains all functions related to managing servers in the DCIM system,
 * including server creation, editing, deletion, bulk operations, and rack assignments.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, configurations)
 * - dcim-sidebar.php (navigation interface)
 * - dcim-locations.php (rack and location dependencies)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * Main server management interface with comprehensive features
 * 
 * Provides bulk server addition, individual server editing, device assignment
 * to racks, and advanced form validation with AJAX support.
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_manage_servers($modulelink) {
    // Debug logging at the very start
    error_log("DCIM: ===== dcim_manage_servers ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-server::before { content: "◼"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-magic::before { content: "⚡"; }
    .fas.fa-eye-slash::before { content: "⊘"; }
    .fas.fa-folder::before { content: "▦"; }
    .fas.fa-network-wired::before { content: "≋"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';
    
    $rack_filter = $_GET['rack_id'] ?? null;
    $edit_server_id = $_GET['edit'] ?? null;
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: rack_filter = " . ($rack_filter ?? 'null'));
    error_log("DCIM: edit_server_id = " . ($edit_server_id ?? 'null'));
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: GET params: " . print_r($_GET, true));
    error_log("DCIM: POST params: " . print_r($_POST, true));
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle device assignment
    if ($_GET['action'] == 'assign_device') {
        $device_id = $_GET['device_id'];
        $rack_id = $_GET['rack_id'];
        $unit = $_GET['unit'];
        
        if ($device_id && $rack_id && $unit) {
            try {
                Capsule::table('dcim_servers')
                    ->where('id', $device_id)
                    ->update([
                        'rack_id' => $rack_id,
                        'start_unit' => $unit,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                // Redirect back to rack view
                echo '<script>window.location.href = "' . $modulelink . '&action=rack_view&rack_id=' . $rack_id . '";</script>';
                return;
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">Error assigning device: ' . $e->getMessage() . '</div>';
            }
        }
    }
    
    // Handle bulk server addition
    error_log("DCIM: Checking for bulk server addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_servers') {
        error_log("DCIM: BULK SERVER ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk server addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values (do this once, not per server)
        try {
            Capsule::schema()->table('dcim_servers', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['servers']) || !is_array($_POST['servers'])) {
            error_log("DCIM: No servers data found in POST");
            echo '<div style="color: red;">No server data received. Please fill in at least one server label.</div>';
            return;
        }
        
        foreach ($_POST['servers'] as $index => $server_data) {
            // Check if all required fields are present
            $has_label = !empty(trim($server_data['label'] ?? ''));
            $has_cpu = !empty($server_data['cpu_model'] ?? '');
            $has_ram = !empty($server_data['ram_config'] ?? '');
            $has_city = !empty($server_data['city'] ?? '');
            $has_rack = !empty($server_data['rack_id'] ?? '');
            
            // Skip rows that don't have any meaningful data
            if (!$has_label && !$has_cpu && !$has_ram && !$has_city && !$has_rack) {
                continue;
            }
            
            // Only process servers that have ALL required fields
            if (!$has_label || !$has_cpu || !$has_ram || !$has_city || !$has_rack) {
                error_log("DCIM: Skipping server $index - missing required fields. Label: $has_label, CPU: $has_cpu, RAM: $has_ram, City: $has_city, Rack: $has_rack");
                $error_count++;
                continue;
            }
            
            error_log("DCIM: Processing server $index with label: " . ($server_data['label'] ?? 'empty'));
            
            try {
                // Create a label from user input or use default Device number
                $label = !empty(trim($server_data['label'])) ? trim($server_data['label']) : 'Device ' . ($index + 1);
                
                Capsule::table('dcim_servers')->insert([
                    'name' => $label,
                    'hostname' => $server_data['hostname'] ?? null,
                    'rack_id' => !empty($server_data['rack_id']) ? $server_data['rack_id'] : null,
                    'start_unit' => !empty($server_data['start_unit']) ? $server_data['start_unit'] : null,
                    'unit_size' => !empty($server_data['unit_size']) ? $server_data['unit_size'] : 1,
                    'make' => $server_data['cpu_model'] ?? null,
                    'model' => $server_data['cpu_model'] ?? null,
                    'specifications' => $server_data['ram_config'] ?? null,
                    'ip_address' => $server_data['ipmi_address'] ?? null,
                    'status' => $server_data['status'] ?? 'available',
                    'serial_number' => $server_data['mac_address'] ?? null,
                    'notes' => 'IPMI Password: ' . ($server_data['ipmi_password'] ?? ''),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding server " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count servers");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' server(s) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' servers added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
            echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=servers_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' server(s) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Handle single server update
    if ($_POST['action'] == 'update_server' && $edit_server_id) {
        try {
            Capsule::table('dcim_servers')
                ->where('id', $edit_server_id)
                ->update([
                    'rack_id' => $_POST['rack_id'] ?: null,
                    'name' => $_POST['name'],
                    'hostname' => $_POST['hostname'],
                    'start_unit' => $_POST['start_unit'] ?: null,
                    'unit_size' => $_POST['unit_size'] ?: 1,
                    'make' => $_POST['make'],
                    'model' => $_POST['model'],
                    'serial_number' => $_POST['serial_number'],
                    'specifications' => $_POST['specifications'],
                    'power_consumption' => $_POST['power_consumption'] ?: 0,
                    'ip_address' => $_POST['ip_address'],
                    'client_id' => $_POST['client_id'] ?: null,
                    'service_id' => $_POST['service_id'] ?: null,
                    'notes' => $_POST['notes'],
                    'status' => $_POST['status'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            echo '<div class="alert alert-success">Server updated successfully!</div>';
            $edit_server_id = null; // Reset edit mode
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error updating server: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_servers')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Server deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting server: ' . $e->getMessage() . '</div>';
        }
    }
    
    // Get data for form
    try {
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
        
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        
        $edit_server = null;
        if ($edit_server_id) {
            $edit_server = Capsule::table('dcim_servers')->where('id', $edit_server_id)->first();
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $racks = collect([]);
        $locations = collect([]);
        $edit_server = null;
    }
    
    // If editing a server, show the edit form
    if ($edit_server) {
        dcim_show_server_edit_form($modulelink, $edit_server, $racks, $locations);
        return;
    }
    
    // Show bulk server addition interface
    dcim_show_bulk_server_form($modulelink, $racks, $locations, $is_ajax);
}

/**
 * Display the server editing form
 * 
 * @param string $modulelink The WHMCS module link
 * @param object $server The server object to edit
 * @param array $racks Available racks
 * @param array $locations Available locations
 */
function dcim_show_server_edit_form($modulelink, $server, $racks, $locations) {
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-server::before { content: "◼"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-magic::before { content: "⚡"; }
    .fas.fa-eye-slash::before { content: "⊘"; }
    .fas.fa-folder::before { content: "▦"; }
    .fas.fa-network-wired::before { content: "≋"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Edit Server</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Edit form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px;">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Edit Server Details</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="update_server">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Server Name *</label>';
    echo '<input type="text" name="name" value="' . htmlspecialchars($server->name) . '" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Hostname</label>';
    echo '<input type="text" name="hostname" value="' . htmlspecialchars($server->hostname) . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Rack</label>';
    echo '<select name="rack_id" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="">Unassigned</option>';
    foreach ($racks as $rack) {
        $selected = ($server->rack_id == $rack->id) ? 'selected' : '';
        echo '<option value="' . $rack->id . '" ' . $selected . '>' . htmlspecialchars($rack->location_name . ' - ' . $rack->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Start Unit</label>';
    echo '<input type="number" name="start_unit" value="' . $server->start_unit . '" min="1" max="42" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Unit Size</label>';
    echo '<input type="number" name="unit_size" value="' . $server->unit_size . '" min="1" max="8" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Make</label>';
    echo '<input type="text" name="make" value="' . htmlspecialchars($server->make) . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Model</label>';
    echo '<input type="text" name="model" value="' . htmlspecialchars($server->model) . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Serial Number</label>';
    echo '<input type="text" name="serial_number" value="' . htmlspecialchars($server->serial_number) . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">IP Address</label>';
    echo '<input type="text" name="ip_address" value="' . htmlspecialchars($server->ip_address) . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Consumption (W)</label>';
    echo '<input type="number" name="power_consumption" value="' . $server->power_consumption . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Client ID</label>';
    echo '<input type="number" name="client_id" value="' . $server->client_id . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Service ID</label>';
    echo '<input type="number" name="service_id" value="' . $server->service_id . '" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Status</label>';
    echo '<select name="status" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    $statuses = ['online', 'offline', 'maintenance', 'provisioning'];
    foreach ($statuses as $status) {
        $selected = ($server->status == $status) ? 'selected' : '';
        echo '<option value="' . $status . '" ' . $selected . '>' . ucfirst($status) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Specifications</label>';
    echo '<textarea name="specifications" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;">' . htmlspecialchars($server->specifications) . '</textarea>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Notes</label>';
    echo '<textarea name="notes" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;">' . htmlspecialchars($server->notes) . '</textarea>';
    echo '</div>';
    
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-save"></i> Update Server';
    echo '</button>';
    echo '<button type="button" onclick="goBack()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">';
    echo '<i class="fas fa-times"></i> Cancel';
    echo '</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    </script>';
}

/**
 * Display bulk server addition form with comprehensive features
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param bool $is_ajax Whether this is an AJAX request
 */
function dcim_show_bulk_server_form($modulelink, $racks, $locations, $is_ajax) {
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-server::before { content: "◼"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-magic::before { content: "⚡"; }
    .fas.fa-eye-slash::before { content: "⊘"; }
    .fas.fa-folder::before { content: "▦"; }
    .fas.fa-network-wired::before { content: "≋"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';
    
    // Get sample data for dropdowns
    try {
        $cpu_models = Capsule::table('dcim_cpu_models')->where('active', true)->get();
        $ram_configs = Capsule::table('dcim_ram_configs')->where('active', true)->get();
    } catch (Exception $e) {
        $cpu_models = collect([]);
        $ram_configs = collect([]);
    }
    
    // If this is an AJAX request, show modal interface
    if ($is_ajax) {
        dcim_show_ajax_server_modal($modulelink, $racks, $locations, $cpu_models, $ram_configs);
        return;
    }
    
    // Show regular page interface
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Server Management</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Bulk server form
    dcim_render_bulk_server_form($modulelink, $racks, $locations, $cpu_models, $ram_configs, false);
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Display server listing table with management features
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_servers_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-server::before { content: "◼"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-magic::before { content: "⚡"; }
    .fas.fa-eye-slash::before { content: "⊘"; }
    .fas.fa-folder::before { content: "▦"; }
    .fas.fa-network-wired::before { content: "≋"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';
    
    try {
        $servers = Capsule::table('dcim_servers')
            ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_servers.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_servers.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching servers: ' . $e->getMessage() . '</div>';
        $servers = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Servers</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="showAddServerModal()" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Server</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Servers table
    if (count($servers) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Server</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Hardware</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">IP Address</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($servers as $server) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($server->name) . '</div>';
            if ($server->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($server->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->location_name ? htmlspecialchars($server->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->rack_name ? htmlspecialchars($server->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($server->start_unit) {
                echo 'U' . $server->start_unit;
                if ($server->unit_size > 1) {
                    echo '-' . ($server->start_unit + $server->unit_size - 1);
                }
                echo ' (' . $server->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($server->make || $server->model) {
                echo htmlspecialchars($server->make . ' ' . $server->model);
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->ip_address ? htmlspecialchars($server->ip_address) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$server->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($server->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editServer(' . $server->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($server->rack_id) {
                echo '<button onclick="viewInRack(' . $server->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-server"></i>';
        echo '<h3>No Servers Found</h3>';
        echo '<p>Add your first server to get started</p>';
        echo '<button onclick="showAddServerModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Server';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Server functionality
    echo '<div id="addServerModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    dcim_generate_servers_javascript($modulelink);
}

/**
 * Display AJAX server modal interface
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param array $cpu_models Available CPU models
 * @param array $ram_configs Available RAM configurations
 */
function dcim_show_ajax_server_modal($modulelink, $racks, $locations, $cpu_models, $ram_configs) {
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-save::before { content: "💾"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-server::before { content: "◼"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-magic::before { content: "⚡"; }
    .fas.fa-eye-slash::before { content: "⊘"; }
    .fas.fa-folder::before { content: "▦"; }
    .fas.fa-network-wired::before { content: "≋"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';
    
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; max-width: 1200px; width: 100%; max-height: 90vh; overflow-y: auto;">';
    echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb;">';
    echo '<div style="display: flex; justify-content: between; align-items: center;">';
    echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Add Servers</h3>';
    echo '<button onclick="closeModal()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6b7280;">&times;</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div style="padding: 24px;">';
    dcim_render_bulk_server_form($modulelink, $racks, $locations, $cpu_models, $ram_configs, true);
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Render bulk server form with comprehensive features
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param array $cpu_models Available CPU models
 * @param array $ram_configs Available RAM configurations
 * @param bool $is_ajax Whether this is an AJAX request
 */
function dcim_render_bulk_server_form($modulelink, $racks, $locations, $cpu_models, $ram_configs, $is_ajax) {
    echo '<form id="bulkServerForm" method="post" action="' . $modulelink . '&action=servers" onsubmit="return validateBulkServerForm()">';
    echo '<input type="hidden" name="action" value="bulk_add_servers">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1">';
    }
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<h4 style="margin: 0 0 16px 0; color: #374151; font-weight: 600;">Server Configuration</h4>';
    echo '<p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">Add multiple servers at once. Fill in at least the required fields (Label, CPU Model, RAM Config, City, Rack) for each server you want to add.</p>';
    echo '</div>';
    
    // Server rows container
    echo '<div id="serverRows" style="margin-bottom: 24px;">';
    
    // Add initial server row
    dcim_render_server_row(0, $racks, $locations, $cpu_models, $ram_configs);
    
    echo '</div>';
    
    // Add row button
    echo '<div style="margin-bottom: 24px;">';
    echo '<button type="button" onclick="addServerRow()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">';
    echo '<i class="fas fa-plus"></i> Add Another Server';
    echo '</button>';
    echo '</div>';
    
    // Submit button
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end;">';
    echo '<button type="button" onclick="closeModal()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #10b981; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600;">';
    echo '<i class="fas fa-save"></i> Add Servers';
    echo '</button>';
    echo '</div>';
    echo '</form>';
    
    // Add JavaScript for form functionality
    echo '<script>
    let serverRowCount = 10;
    
    function addServerRow() {
        const container = document.getElementById("serverRows");
        const newRow = document.createElement("div");
        newRow.className = "server-row";
        newRow.style.cssText = "border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 16px; background: #f9fafb;";
        
        // Clone the first row and update IDs
        const firstRow = container.querySelector(".server-row");
        newRow.innerHTML = firstRow.innerHTML;
        
        // Update all input names and IDs
        const inputs = newRow.querySelectorAll("input, select");
        inputs.forEach(input => {
            if (input.name) {
                input.name = input.name.replace("[0]", "[" + serverRowCount + "]");
            }
            if (input.id) {
                input.id = input.id.replace("_0", "_" + serverRowCount);
            }
        });
        
        // Clear the values
        inputs.forEach(input => {
            if (input.type !== "hidden") {
                input.value = "";
            }
        });
        
        container.appendChild(newRow);
        serverRowCount++;
    }
    
    function validateBulkServerForm() {
        console.log("validateBulkServerForm called");
        
        // Get all rows from the basic info tab
        const basicRows = document.querySelectorAll("#basicInfoContainer tr");
        let hasValidRow = false;
        
        basicRows.forEach((row, index) => {
            const labelInput = row.querySelector("input[name*=\'[label]\']");
            const cpuSelect = row.querySelector("select[name*=\'[cpu_model]\']");
            const ramSelect = row.querySelector("select[name*=\'[ram_config]\']");
            
            if (labelInput && cpuSelect && ramSelect) {
                const label = labelInput.value.trim();
                const cpu = cpuSelect.value;
                const ram = ramSelect.value;
                
                console.log("Row " + (index + 1) + " data:", { label, cpu, ram });
                
                // Check if this row has any data
                if (label || cpu || ram) {
                    // If it has some data, it must have all required fields
                    if (!label || !cpu || !ram) {
                        alert("Row " + (index + 1) + " is missing required fields. Please fill in Label, CPU Model, and RAM Config for each server you want to add.");
                        return false;
                    }
                    hasValidRow = true;
                }
            }
        });
        
        if (!hasValidRow) {
            alert("Please fill in at least one server with all required fields (Label, CPU Model, RAM Config).");
            return false;
        }
        
        console.log("Form validation passed, submitting...");
        return true;
    }
    </script>';
}

/**
 * Render a single server row for the bulk form
 * 
 * @param int $index The row index
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param array $cpu_models Available CPU models
 * @param array $ram_configs Available RAM configurations
 */
function dcim_render_server_row($index, $racks, $locations, $cpu_models, $ram_configs) {
    echo '<div class="server-row" style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 16px; background: #f9fafb;">';
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">';
    
    // Label
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Label *</label>';
    echo '<input type="text" name="servers[' . $index . '][label]" placeholder="Server Label" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // Hostname
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Hostname</label>';
    echo '<input type="text" name="servers[' . $index . '][hostname]" placeholder="hostname.example.com" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // CPU Model
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">CPU Model *</label>';
    echo '<select name="servers[' . $index . '][cpu_model]" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '<option value="">Select CPU Model</option>';
    foreach ($cpu_models as $cpu) {
        echo '<option value="' . htmlspecialchars($cpu->name) . '">' . htmlspecialchars($cpu->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    // RAM Configuration
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">RAM Config *</label>';
    echo '<select name="servers[' . $index . '][ram_config]" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '<option value="">Select RAM Configuration</option>';
    foreach ($ram_configs as $ram) {
        echo '<option value="' . htmlspecialchars($ram->name) . '">' . htmlspecialchars($ram->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    // City
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">City *</label>';
    echo '<select name="servers[' . $index . '][city]" onchange="updateRackOptions(this.value, ' . $index . ')" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '<option value="">Select City</option>';
    foreach ($locations as $location) {
        echo '<option value="' . htmlspecialchars($location->city) . '">' . htmlspecialchars($location->city) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    // Rack
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Rack *</label>';
    echo '<select name="servers[' . $index . '][rack_id]" id="rack_' . $index . '" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '<option value="">Select Rack</option>';
    echo '</select>';
    echo '</div>';
    
    // Start Unit
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Start Unit</label>';
    echo '<input type="number" name="servers[' . $index . '][start_unit]" min="1" max="42" placeholder="1" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // Unit Size
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Unit Size</label>';
    echo '<input type="number" name="servers[' . $index . '][unit_size]" min="1" max="8" value="1" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // IPMI Address
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">IPMI Address</label>';
    echo '<input type="text" name="servers[' . $index . '][ipmi_address]" placeholder="*************" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // IPMI Password
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">IPMI Password</label>';
    echo '<input type="password" name="servers[' . $index . '][ipmi_password]" placeholder="Password" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // MAC Address
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">MAC Address</label>';
    echo '<input type="text" name="servers[' . $index . '][mac_address]" placeholder="00:11:22:33:44:55" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    
    // Status
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px; font-size: 14px;">Status</label>';
    echo '<select name="servers[' . $index . '][status]" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '<option value="available">Available</option>';
    echo '<option value="online">Online</option>';
    echo '<option value="offline">Offline</option>';
    echo '<option value="maintenance">Maintenance</option>';
    echo '<option value="provisioning">Provisioning</option>';
    echo '</select>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Add JavaScript for rack filtering
    echo '<script>
    function updateRackOptions(city, rowIndex) {
        const rackSelect = document.getElementById("rack_" + rowIndex);
        rackSelect.innerHTML = "<option value=\'\'>Select Rack</option>";
        
        if (!city) return;
        
        const racks = ' . json_encode($racks) . ';
        racks.forEach(rack => {
            if (rack.location_name && rack.location_name.includes(city)) {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.location_name + " - " + rack.name;
                rackSelect.appendChild(option);
            }
        });
    }
    </script>';
}

/**
 * Generate JavaScript for server management functionality
 * 
 * @param string $modulelink The WHMCS module link
 */
/**
 * Generate JavaScript for server management functionality
 * 
 * @param string $modulelink The WHMCS module link
 */
function dcim_generate_servers_javascript($modulelink) {
    echo '<script>
    function editServer(serverId) {
        window.location.href = "' . $modulelink . '&action=servers&edit=" + serverId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddServerModal() {
        // Create modal content directly instead of using AJAX
        const modalContainer = document.getElementById("addServerModalContainer");
        
        // Function to generate multiple rows for Basic Info tab
        function generateBasicInfoRows() {
            let rowsHTML = "";
            for (let i = 0; i < 10; i++) {
                rowsHTML += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 16px; color: #374151; font-weight: 500;">${i + 1}</td>
                        <td style="padding: 16px;">
                            <input type="text" name="servers[${i}][label]" class="form-control device-label-input" placeholder="Device ${i + 1}" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <select name="servers[${i}][cpu_model]" class="form-control cpu-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                                    <option value="">Select CPU Model</option>
                                    <option value="Intel Xeon E5-2680">Intel Xeon E5-2680</option>
                                    <option value="Intel Xeon E5-2690">Intel Xeon E5-2690</option>
                                    <option value="AMD EPYC 7302">AMD EPYC 7302</option>
                                </select>
                                <button type="button" class="btn btn-sm" onclick="showAddCpuModal()" title="Add CPU Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                            </div>
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <select name="servers[${i}][ram_config]" class="form-control ram-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                                    <option value="">Select RAM Configuration</option>
                                    <option value="32GB DDR4">32GB DDR4</option>
                                    <option value="64GB DDR4">64GB DDR4</option>
                                    <option value="128GB DDR4">128GB DDR4</option>
                                </select>
                                <button type="button" class="btn btn-sm" onclick="showAddRamModal()" title="Add RAM Configuration" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                            </div>
                        </td>
                        <td style="padding: 16px;">
                            <select name="servers[${i}][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                                <option value="available">Available</option>
                                <option value="provisioning">Provisioning</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="offline">Offline</option>
                            </select>
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 6px;">
                                <button type="button" class="btn btn-sm" onclick="removeBasicRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                            </div>
                        </td>
                    </tr>
                `;
            }
            return rowsHTML;
        }

        // Function to generate multiple rows for Network tab
        function generateNetworkRows() {
            let rowsHTML = "";
            for (let i = 0; i < 10; i++) {
                rowsHTML += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 16px; color: #374151; font-weight: 500;">${i + 1}</td>
                        <td style="padding: 16px;"><span id="device-name-network-${i + 1}" style="color: #111827; font-weight: 500;">Device ${i + 1}</span></td>
                        <td style="padding: 16px;">
                            <select name="servers[${i}][switch_name]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                                <option value="">Select Switch</option>
                                <option value="SW-CORE-01">SW-CORE-01 (Core Switch)</option>
                                <option value="SW-CORE-02">SW-CORE-02 (Core Switch)</option>
                                <option value="SW-TOR-A01">SW-TOR-A01 (Top of Rack)</option>
                                <option value="SW-TOR-A02">SW-TOR-A02 (Top of Rack)</option>
                            </select>
                        </td>
                        <td style="padding: 16px;">
                            <input type="text" name="servers[${i}][ports]" class="form-control" placeholder="Select a switch first" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: #f9fafb;" readonly>
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 6px;">
                                <button type="button" class="btn btn-sm" onclick="autoAssignPorts(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>
                                <button type="button" class="btn btn-sm" onclick="removeNetworkRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                            </div>
                        </td>
                    </tr>
                `;
            }
            return rowsHTML;
        }

        // Function to generate multiple rows for Location tab
        function generateLocationRows() {
            let rowsHTML = "";
            for (let i = 0; i < 10; i++) {
                rowsHTML += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 16px; color: #374151; font-weight: 500;">${i + 1}</td>
                        <td style="padding: 16px;"><span id="device-name-location-${i + 1}" style="color: #111827; font-weight: 500;">Device ${i + 1}</span></td>
                        <td style="padding: 16px;">
                            <select name="servers[${i}][city]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                                <option value="">Select City</option>
                                <option value="San Jose">San Jose</option>
                                <option value="Frankfurt">Frankfurt</option>
                            </select>
                        </td>
                        <td style="padding: 16px;">
                            <select name="servers[${i}][rack_id]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                                <option value="">Select Rack</option>
                                <option value="1">San Jose - A01</option>
                                <option value="2">San Jose - A02</option>
                                <option value="3">Frankfurt - A01</option>
                                <option value="4">Frankfurt - A02</option>
                            </select>
                        </td>
                        <td style="padding: 16px;">
                            <input type="number" name="servers[${i}][start_unit]" min="1" max="42" placeholder="1" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <input type="number" name="servers[${i}][unit_size]" min="1" max="8" value="1" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 6px;">
                                <button type="button" class="btn btn-sm" onclick="autoAssignPosition(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>
                                <button type="button" class="btn btn-sm" onclick="removeLocationRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                            </div>
                        </td>
                    </tr>
                `;
            }
            return rowsHTML;
        }

        // Function to generate multiple rows for IPMI tab
        function generateIpmiRows() {
            let rowsHTML = "";
            for (let i = 0; i < 10; i++) {
                rowsHTML += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 16px; color: #374151; font-weight: 500;">${i + 1}</td>
                        <td style="padding: 16px;"><span id="device-name-ipmi-${i + 1}" style="color: #111827; font-weight: 500;">Device ${i + 1}</span></td>
                        <td style="padding: 16px;">
                            <input type="text" name="servers[${i}][ipmi_address]" placeholder="*************" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <input type="text" name="servers[${i}][ipmi_username]" placeholder="admin" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="password" name="servers[${i}][ipmi_password]" placeholder="Password" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                                <button type="button" class="btn btn-sm" onclick="togglePassword(this)" title="Show/Hide" style="background: #6b7280; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-eye"></i></button>
                            </div>
                        </td>
                        <td style="padding: 16px;">
                            <input type="text" name="servers[${i}][mac_address]" placeholder="00:11:22:33:44:55" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                        </td>
                        <td style="padding: 16px;">
                            <div style="display: flex; gap: 6px;">
                                <button type="button" class="btn btn-sm" onclick="removeIpmiRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                            </div>
                        </td>
                    </tr>
                `;
            }
            return rowsHTML;
        }
        
        // Generate the modal HTML directly with tabs and 10 initial rows
        const modalHTML = `
            <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">
                <div style="background: white; border-radius: 12px; max-width: 1400px; width: 100%; max-height: 90vh; display: flex; flex-direction: column;">
                    <div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                        <h3 style="margin: 0; color: #111827; font-weight: 600;">Add Servers</h3>
                        <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>
                    </div>
                    
                    <!-- Tab navigation -->
                    <div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb; flex-shrink: 0;">
                        <ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">
                            <li role="presentation" class="active" style="margin: 0; list-style: none;">
                                <a href="#basic-info" aria-controls="basic-info" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: #4f46e5; font-weight: 500; text-decoration: none; border-bottom: 3px solid #4f46e5; margin-bottom: -1px;">
                                    <i class="fas fa-folder" style="font-size: 14px;"></i>
                                    Basic Info
                                </a>
                            </li>
                            <li role="presentation" style="margin: 0; list-style: none;">
                                <a href="#network" aria-controls="network" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: #6b7280; font-weight: 500; text-decoration: none; border-bottom: 3px solid transparent; margin-bottom: -1px;">
                                    <i class="fas fa-network-wired" style="font-size: 14px;"></i>
                                    Network
                                </a>
                            </li>
                            <li role="presentation" style="margin: 0; list-style: none;">
                                <a href="#location" aria-controls="location" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: #6b7280; font-weight: 500; text-decoration: none; border-bottom: 3px solid transparent; margin-bottom: -1px;">
                                    <i class="fas fa-globe" style="font-size: 14px;"></i>
                                    Location
                                </a>
                            </li>
                            <li role="presentation" style="margin: 0; list-style: none;">
                                <a href="#ipmi" aria-controls="ipmi" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: #6b7280; font-weight: 500; text-decoration: none; border-bottom: 3px solid transparent; margin-bottom: -1px;">
                                    <i class="fas fa-server" style="font-size: 14px;"></i>
                                    IPMI
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Form -->
                    <form method="post" id="bulkServerForm" onsubmit="return validateBulkServerForm()" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">
                        <input type="hidden" name="action" value="bulk_add_servers" id="actionField">
                        <input type="hidden" name="ajax" value="1" id="ajaxField">
                        
                        <!-- Tab content -->
                        <div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">
                            
                            <!-- Basic Info Tab -->
                            <div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">
                                <!-- Top action buttons -->
                                <div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">
                                    <button type="button" class="btn" onclick="addBasicRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-plus"></i> Add Row
                                    </button>
                                    <button type="button" class="btn" onclick="addFiveBasicRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-th"></i> Add 5 Rows
                                    </button>
                                </div>
                                
                                <div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">
                                    <div class="table-responsive">
                                        <table class="table" style="margin: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label*</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">CPU*</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">RAM*</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Status</th>
                                                    <th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="basicInfoContainer">
                                                ${generateBasicInfoRows()}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Network Tab -->
                            <div role="tabpanel" class="tab-pane" id="network" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">
                                <!-- Top action buttons -->
                                <div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">
                                    <button type="button" class="btn" onclick="addNetworkRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-plus"></i> Add Row
                                    </button>
                                    <button type="button" class="btn" onclick="addFiveNetworkRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-th"></i> Add 5 Rows
                                    </button>
                                </div>
                                
                                <div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">
                                    <div class="table-responsive">
                                        <table class="table" style="margin: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Switch</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Ports</th>
                                                    <th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="networkContainer">
                                                ${generateNetworkRows()}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Location Tab -->
                            <div role="tabpanel" class="tab-pane" id="location" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">
                                <!-- Top action buttons -->
                                <div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">
                                    <button type="button" class="btn" onclick="addLocationRowFunc()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-plus"></i> Add Row
                                    </button>
                                    <button type="button" class="btn" onclick="addFiveLocationRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-th"></i> Add 5 Rows
                                    </button>
                                </div>
                                
                                <div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">
                                    <div class="table-responsive">
                                        <table class="table" style="margin: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">City*</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Rack*</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Top Position</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Size U</th>
                                                    <th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="locationContainer">
                                                ${generateLocationRows()}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- IPMI Tab -->
                            <div role="tabpanel" class="tab-pane" id="ipmi" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">
                                <!-- Top action buttons -->
                                <div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">
                                    <button type="button" class="btn" onclick="addIpmiRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-plus"></i> Add Row
                                    </button>
                                    <button type="button" class="btn" onclick="addFiveIpmiRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-th"></i> Add 5 Rows
                                    </button>
                                </div>
                                
                                <div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">
                                    <div class="table-responsive">
                                        <table class="table" style="margin: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">IPMI Address</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Username</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Password</th>
                                                    <th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">MAC Address</th>
                                                    <th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="ipmiContainer">
                                                ${generateIpmiRows()}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Bottom action buttons -->
                        <div style="padding: 24px 32px; border-top: 1px solid #e5e7eb; display: flex; gap: 12px; justify-content: flex-end; flex-shrink: 0;">
                            <button type="button" onclick="closeModal()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">Cancel</button>
                            <button type="submit" style="background: #10b981; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-save"></i> Add Servers
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        modalContainer.innerHTML = modalHTML;
        modalContainer.style.display = "block";
        
        // Initialize modal functionality
        initializeAjaxModal();
    }
    
    function closeModal() {
        const modalContainer = document.getElementById("addServerModalContainer");
        modalContainer.style.display = "none";
        modalContainer.innerHTML = "";
    }
    
    // Initialize AJAX modal functionality
    function initializeAjaxModal() {
        console.log("initializeAjaxModal called");
        
        // Make sure all the global functions are available
        window.closeModal = function() {
            const modalContainer = document.getElementById("addServerModalContainer");
            if (modalContainer) {
                modalContainer.style.display = "none";
                modalContainer.innerHTML = "";
            }
        };
        
        // Initialize tabs functionality
        function initTabs() {
            const tabs = document.querySelectorAll(".nav-tabs a");
            console.log("Initializing tabs, found:", tabs.length);
            
            tabs.forEach(function(tab) {
                tab.addEventListener("click", function(e) {
                    e.preventDefault();
                    console.log("Tab clicked:", this.textContent);
                    
                    // Remove active class from all tabs
                    document.querySelectorAll(".nav-tabs li").forEach(function(li) {
                        li.classList.remove("active");
                    });
                    document.querySelectorAll(".tab-pane").forEach(function(pane) {
                        pane.style.display = "none";
                    });
                    
                    // Add active class to clicked tab
                    this.parentElement.classList.add("active");
                    const targetId = this.getAttribute("href").substring(1);
                    const targetPane = document.getElementById(targetId);
                    console.log("Switching to tab:", targetId, "found:", !!targetPane);
                    
                    if (targetPane) {
                        targetPane.style.display = "flex";
                        targetPane.style.flexDirection = "column";
                        targetPane.style.overflow = "hidden";
                    }
                    
                    // Update tab styling
                    document.querySelectorAll(".nav-tabs a").forEach(function(a) {
                        a.style.color = "#6b7280";
                        a.style.borderBottomColor = "transparent";
                    });
                    this.style.color = "#4f46e5";
                    this.style.borderBottomColor = "#4f46e5";
                });
            });
        }
        
        // Initialize tabs first
        initTabs();
        
        // Look for the form within the modal container
        const modalContainer = document.getElementById("addServerModalContainer");
        console.log("Modal container found:", !!modalContainer);
        if (modalContainer) {
            console.log("Modal container HTML:", modalContainer.innerHTML.substring(0, 1000));
            console.log("All forms in modal:", modalContainer.querySelectorAll("form"));
        }
        const bulkForm = modalContainer ? modalContainer.querySelector("#bulkServerForm") : null;
        if (bulkForm) {
            console.log("AJAX modal form found - submission handled by onsubmit attribute");
        } else {
            console.log("AJAX modal form not found");
        }
    }
    
    // Tab-specific row management functions
    function addBasicRow() {
        const container = document.getElementById("basicInfoContainer");
        const rowCount = container.children.length + 1;
        const newRow = createBasicRow(rowCount);
        container.appendChild(newRow);
        updateRowNumbers("basicInfoContainer");
    }
    
    function addFiveBasicRows() {
        for (let i = 0; i < 5; i++) {
            addBasicRow();
        }
    }
    
    function removeBasicRow(button) {
        const row = button.closest("tr");
        row.remove();
        updateRowNumbers("basicInfoContainer");
    }
    
    function addNetworkRow() {
        const container = document.getElementById("networkContainer");
        const rowCount = container.children.length + 1;
        const newRow = createNetworkRow(rowCount);
        container.appendChild(newRow);
        updateRowNumbers("networkContainer");
    }
    
    function addFiveNetworkRows() {
        for (let i = 0; i < 5; i++) {
            addNetworkRow();
        }
    }
    
    function removeNetworkRow(button) {
        const row = button.closest("tr");
        row.remove();
        updateRowNumbers("networkContainer");
    }
    
    function addLocationRowFunc() {
        const container = document.getElementById("locationContainer");
        const rowCount = container.children.length + 1;
        const newRow = createLocationRow(rowCount);
        container.appendChild(newRow);
        updateRowNumbers("locationContainer");
    }
    
    function addFiveLocationRows() {
        for (let i = 0; i < 5; i++) {
            addLocationRowFunc();
        }
    }
    
    function removeLocationRow(button) {
        const row = button.closest("tr");
        row.remove();
        updateRowNumbers("locationContainer");
    }
    
    function addIpmiRow() {
        const container = document.getElementById("ipmiContainer");
        const rowCount = container.children.length + 1;
        const newRow = createIpmiRow(rowCount);
        container.appendChild(newRow);
        updateRowNumbers("ipmiContainer");
    }
    
    function addFiveIpmiRows() {
        for (let i = 0; i < 5; i++) {
            addIpmiRow();
        }
    }
    
    function removeIpmiRow(button) {
        const row = button.closest("tr");
        row.remove();
        updateRowNumbers("ipmiContainer");
    }
    
    function updateRowNumbers(containerId) {
        const container = document.getElementById(containerId);
        const rows = container.children;
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const numberCell = row.querySelector("td:first-child");
            if (numberCell) {
                numberCell.textContent = i + 1;
            }
        }
    }
    
    function createBasicRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.cssText = "border-bottom: 1px solid #f3f4f6;";
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][label]" class="form-control device-label-input" placeholder="Device ${rowNumber}" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <select name="servers[${rowNumber-1}][cpu_model]" class="form-control cpu-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                        <option value="">Select CPU Model</option>
                        <option value="Intel Xeon E5-2680">Intel Xeon E5-2680</option>
                        <option value="Intel Xeon E5-2690">Intel Xeon E5-2690</option>
                        <option value="AMD EPYC 7302">AMD EPYC 7302</option>
                    </select>
                    <button type="button" class="btn btn-sm" onclick="showAddCpuModal()" title="Add CPU Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <select name="servers[${rowNumber-1}][ram_config]" class="form-control ram-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                        <option value="">Select RAM Configuration</option>
                        <option value="32GB DDR4">32GB DDR4</option>
                        <option value="64GB DDR4">64GB DDR4</option>
                        <option value="128GB DDR4">128GB DDR4</option>
                    </select>
                    <button type="button" class="btn btn-sm" onclick="showAddRamModal()" title="Add RAM Configuration" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <select name="servers[${rowNumber-1}][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                    <option value="available">Available</option>
                    <option value="provisioning">Provisioning</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="offline">Offline</option>
                </select>
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 6px;">
                    <button type="button" class="btn btn-sm" onclick="removeBasicRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;
        return row;
    }
    
    function createNetworkRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.cssText = "border-bottom: 1px solid #f3f4f6;";
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;"><span id="device-name-network-${rowNumber}" style="color: #111827; font-weight: 500;">Device ${rowNumber}</span></td>
            <td style="padding: 16px;">
                <select name="servers[${rowNumber-1}][switch_name]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                    <option value="">Select Switch</option>
                    <option value="SW-CORE-01">SW-CORE-01 (Core Switch)</option>
                    <option value="SW-CORE-02">SW-CORE-02 (Core Switch)</option>
                    <option value="SW-TOR-A01">SW-TOR-A01 (Top of Rack)</option>
                    <option value="SW-TOR-A02">SW-TOR-A02 (Top of Rack)</option>
                </select>
            </td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][ports]" class="form-control" placeholder="Select a switch first" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: #f9fafb;" readonly>
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 6px;">
                    <button type="button" class="btn btn-sm" onclick="autoAssignPorts(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>
                    <button type="button" class="btn btn-sm" onclick="removeNetworkRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;
        return row;
    }
    
    function createLocationRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.cssText = "border-bottom: 1px solid #f3f4f6;";
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;"><span id="device-name-location-${rowNumber}" style="color: #111827; font-weight: 500;">Device ${rowNumber}</span></td>
            <td style="padding: 16px;">
                <select name="servers[${rowNumber-1}][city]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                    <option value="">Select City</option>
                    <option value="San Jose">San Jose</option>
                    <option value="Frankfurt">Frankfurt</option>
                </select>
            </td>
            <td style="padding: 16px;">
                <select name="servers[${rowNumber-1}][rack_id]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                    <option value="">Select Rack</option>
                    <option value="1">San Jose - A01</option>
                    <option value="2">San Jose - A02</option>
                    <option value="3">Frankfurt - A01</option>
                    <option value="4">Frankfurt - A02</option>
                </select>
            </td>
            <td style="padding: 16px;">
                <input type="number" name="servers[${rowNumber-1}][start_unit]" min="1" max="42" placeholder="1" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <input type="number" name="servers[${rowNumber-1}][unit_size]" min="1" max="8" value="1" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 6px;">
                    <button type="button" class="btn btn-sm" onclick="autoAssignPosition(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>
                    <button type="button" class="btn btn-sm" onclick="removeLocationRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;
        return row;
    }
    
    function createIpmiRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.cssText = "border-bottom: 1px solid #f3f4f6;";
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;"><span id="device-name-ipmi-${rowNumber}" style="color: #111827; font-weight: 500;">Device ${rowNumber}</span></td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][ipmi_address]" placeholder="*************" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][ipmi_username]" placeholder="admin" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <input type="password" name="servers[${rowNumber-1}][ipmi_password]" placeholder="Password" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                    <button type="button" class="btn btn-sm" onclick="togglePassword(this)" title="Show/Hide" style="background: #6b7280; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-eye"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][mac_address]" placeholder="00:11:22:33:44:55" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 6px;">
                    <button type="button" class="btn btn-sm" onclick="removeIpmiRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;
        return row;
    }
    
    // Utility functions
    function autoAssignPorts(button) {
        const row = button.closest("tr");
        const switchSelect = row.querySelector("select[name*=\'[switch_name]\']");
        const portsInput = row.querySelector("input[name*=\'[ports]\']");
        
        if (switchSelect.value) {
            // Auto-assign ports based on switch
            const switchPorts = {
                "SW-CORE-01": "1-48",
                "SW-CORE-02": "1-48", 
                "SW-TOR-A01": "1-24",
                "SW-TOR-A02": "1-24"
            };
            portsInput.value = switchPorts[switchSelect.value] || "";
            portsInput.style.background = "#ffffff";
        } else {
            alert("Please select a switch first");
        }
    }
    
    function autoAssignPosition(button) {
        const row = button.closest("tr");
        const rackSelect = row.querySelector("select[name*=\'[rack_id]\']");
        const startUnitInput = row.querySelector("input[name*=\'[start_unit]\']");
        
        if (rackSelect.value) {
            // Auto-assign next available position
            startUnitInput.value = Math.floor(Math.random() * 40) + 1;
        } else {
            alert("Please select a rack first");
        }
    }
    
    function togglePassword(button) {
        const input = button.previousElementSibling;
        const icon = button.querySelector("i");
        
        if (input.type === "password") {
            input.type = "text";
            icon.className = "fas fa-eye-slash";
        } else {
            input.type = "password";
            icon.className = "fas fa-eye";
        }
    }
    
    // Add server row functionality
    let serverRowCount = 10; // Start from 10 since we have 10 initial rows
    
    function addServerRow() {
        const container = document.getElementById("serverRows");
        const newRow = document.createElement("div");
        newRow.className = "server-row";
        newRow.style.cssText = "border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 16px; background: #f9fafb;";
        
        // Clone the first row and update IDs
        const firstRow = container.querySelector(".server-row");
        newRow.innerHTML = firstRow.innerHTML;
        
        // Update all input names and IDs
        const inputs = newRow.querySelectorAll("input, select");
        inputs.forEach(input => {
            if (input.name) {
                input.name = input.name.replace("[0]", "[" + serverRowCount + "]");
            }
            if (input.id) {
                input.id = input.id.replace("_0", "_" + serverRowCount);
            }
        });
        
        // Clear the values
        inputs.forEach(input => {
            if (input.type !== "hidden") {
                input.value = "";
            }
        });
        
        container.appendChild(newRow);
        serverRowCount++;
    }
    
    function validateBulkServerForm() {
        console.log("validateBulkServerForm called");
        const rows = document.querySelectorAll(".server-row");
        let hasValidRow = false;
        
        rows.forEach((row, index) => {
            const label = row.querySelector("input[name*=\'[label]\']").value.trim();
            const cpu = row.querySelector("select[name*=\'[cpu_model]\']").value;
            const ram = row.querySelector("select[name*=\'[ram_config]\']").value;
            const city = row.querySelector("select[name*=\'[city]\']").value;
            const rack = row.querySelector("select[name*=\'[rack_id]\']").value;
            
            console.log("Row " + (index + 1) + " data:", { label, cpu, ram, city, rack });
            
            // Check if this row has any data
            if (label || cpu || ram || city || rack) {
                // If it has some data, it must have all required fields
                if (!label || !cpu || !ram || !city || !rack) {
                    alert("Row " + (index + 1) + " is missing required fields. Please fill in all required fields or leave the row completely empty.");
                    return false;
                }
                hasValidRow = true;
            }
        });
        
        if (!hasValidRow) {
            alert("Please fill in at least one server with all required fields.");
            return false;
        }
        
        console.log("Form validation passed, submitting...");
        return true;
    }
    </script>';
}

/**
 * Get server statistics for dashboard
 * 
 * @return array Server statistics
 */
function dcim_get_server_stats() {
    try {
        $total_servers = Capsule::table('dcim_servers')->count();
        $online_servers = Capsule::table('dcim_servers')->where('status', 'online')->count();
        $assigned_servers = Capsule::table('dcim_servers')->whereNotNull('rack_id')->count();
        $unassigned_servers = Capsule::table('dcim_servers')->whereNull('rack_id')->count();
        
        return [
            'total' => $total_servers,
            'online' => $online_servers,
            'assigned' => $assigned_servers,
            'unassigned' => $unassigned_servers,
            'online_percentage' => $total_servers > 0 ? round(($online_servers / $total_servers) * 100, 1) : 0,
            'assignment_percentage' => $total_servers > 0 ? round(($assigned_servers / $total_servers) * 100, 1) : 0
        ];
    } catch (Exception $e) {
        error_log("DCIM: Error getting server stats - " . $e->getMessage());
        return [
            'total' => 0,
            'online' => 0,
            'assigned' => 0,
            'unassigned' => 0,
            'online_percentage' => 0,
            'assignment_percentage' => 0
        ];
    }
}

/**
 * Get all servers for a specific rack
 * 
 * @param int $rack_id The rack ID
 * @return array Array of server objects
 */
function dcim_get_rack_servers($rack_id) {
    try {
        return Capsule::table('dcim_servers')
            ->where('rack_id', $rack_id)
            ->orderBy('start_unit', 'desc')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching rack servers - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Validate server data
 * 
 * @param array $data Server data to validate
 * @return array Array with 'valid' boolean and 'errors' array
 */
function dcim_validate_server_data($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors[] = 'Server name is required';
    }
    
    if (!empty($data['start_unit']) && (!is_numeric($data['start_unit']) || $data['start_unit'] < 1 || $data['start_unit'] > 42)) {
        $errors[] = 'Start unit must be between 1 and 42';
    }
    
    if (!empty($data['unit_size']) && (!is_numeric($data['unit_size']) || $data['unit_size'] < 1 || $data['unit_size'] > 8)) {
        $errors[] = 'Unit size must be between 1 and 8';
    }
    
    if (!empty($data['power_consumption']) && !is_numeric($data['power_consumption'])) {
        $errors[] = 'Power consumption must be a number';
    }
    
    if (!empty($data['ip_address']) && !filter_var($data['ip_address'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid IP address format';
    }
    
    return [
        'valid' => count($errors) == 0,
        'errors' => $errors
    ];
}

/**
 * Create a new server
 * 
 * @param array $data Server data
 * @return array Result array with success/error information
 */
function dcim_create_server($data) {
    try {
        $validation = dcim_validate_server_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $server_id = Capsule::table('dcim_servers')->insertGetId([
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 1,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'specifications' => $data['specifications'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'ip_address' => $data['ip_address'] ?? null,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'offline',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        return ['success' => true, 'server_id' => $server_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating server - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update an existing server
 * 
 * @param int $server_id The server ID
 * @param array $data Updated server data
 * @return array Result array with success/error information
 */
function dcim_update_server($server_id, $data) {
    try {
        $validation = dcim_validate_server_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $updateData = [
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 1,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'specifications' => $data['specifications'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'ip_address' => $data['ip_address'] ?? null,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        Capsule::table('dcim_servers')
            ->where('id', $server_id)
            ->update($updateData);
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error updating server - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Delete a server
 * 
 * @param int $server_id The server ID to delete
 * @return array Result array with success/error information
 */
function dcim_delete_server($server_id) {
    try {
        // Also clean up any IP assignments
        $ip_assignments = Capsule::table('dcim_ip_assignments')
            ->where('device_type', 'server')
            ->where('device_id', $server_id)
            ->get();
            
        foreach ($ip_assignments as $assignment) {
            // Mark IP as available again
            Capsule::table('dcim_ip_addresses')
                ->where('id', $assignment->ip_address_id)
                ->update(['status' => 'available', 'updated_at' => date('Y-m-d H:i:s')]);
                
            // Delete assignment
            Capsule::table('dcim_ip_assignments')->where('id', $assignment->id)->delete();
        }
        
        // Delete the server
        Capsule::table('dcim_servers')->where('id', $server_id)->delete();
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting server - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Assign server to rack position
 * 
 * @param int $server_id The server ID
 * @param int $rack_id The rack ID  
 * @param int $start_unit The starting rack unit
 * @return array Result array with success/error information
 */
function dcim_assign_server_to_rack($server_id, $rack_id, $start_unit) {
    try {
        // Validate rack unit availability
        $server = Capsule::table('dcim_servers')->where('id', $server_id)->first();
        if (!$server) {
            return ['success' => false, 'error' => 'Server not found'];
        }
        
        $rack = Capsule::table('dcim_racks')->where('id', $rack_id)->first();
        if (!$rack) {
            return ['success' => false, 'error' => 'Rack not found'];
        }
        
        $unit_size = $server->unit_size;
        $end_unit = $start_unit + $unit_size - 1;
        
        if ($end_unit > $rack->units) {
            return ['success' => false, 'error' => 'Server does not fit in rack at specified position'];
        }
        
        // Check for conflicts with existing servers
        $conflicts = Capsule::table('dcim_servers')
            ->where('rack_id', $rack_id)
            ->where('id', '!=', $server_id)
            ->whereNotNull('start_unit')
            ->get();
            
        foreach ($conflicts as $conflict) {
            $conflict_start = $conflict->start_unit;
            $conflict_end = $conflict_start + $conflict->unit_size - 1;
            
            // Check if ranges overlap
            if (!($end_unit < $conflict_start || $start_unit > $conflict_end)) {
                return ['success' => false, 'error' => 'Position conflicts with existing server: ' . $conflict->name];
            }
        }
        
        // Update server position
        Capsule::table('dcim_servers')
            ->where('id', $server_id)
            ->update([
                'rack_id' => $rack_id,
                'start_unit' => $start_unit,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error assigning server to rack - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Add CPU Model Modal
if (!function_exists('dcim_render_cpu_ram_modals')) {
function dcim_render_cpu_ram_modals($modulelink) {
    echo '<div id="addCpuModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add CPU Model</h3>';
    echo '<button type="button" onclick="closeAddCpuModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addCpuForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">CPU Model Name *</label>';
    echo '<input type="text" id="cpuName" placeholder="e.g., Intel Xeon E5-2680v4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Manufacturer</label>';
    echo '<input type="text" id="cpuManufacturer" placeholder="e.g., Intel" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Cores</label>';
    echo '<input type="text" id="cpuCores" placeholder="e.g., 16" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Frequency</label>';
    echo '<input type="text" id="cpuFrequency" placeholder="e.g., 2.4GHz" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="cpuDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddCpuModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Add CPU Model</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';

    // Add RAM Configuration Modal
    echo '<div id="addRamModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add RAM Configuration</h3>';
    echo '<button type="button" onclick="closeAddRamModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addRamForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">RAM Configuration Name *</label>';
    echo '<input type="text" id="ramName" placeholder="e.g., 64GB DDR4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Size</label>';
    echo '<input type="text" id="ramSize" placeholder="e.g., 64GB" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Type</label>';
    echo '<input type="text" id="ramType" placeholder="e.g., DDR4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Speed</label>';
    echo '<input type="text" id="ramSpeed" placeholder="e.g., 3200MHz" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="ramDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddRamModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Add RAM Configuration</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
}
}
dcim_render_cpu_ram_modals($modulelink);

// Add/replace JS for modal logic and AJAX
?>
<script>
function showAddCpuModal() {
    document.getElementById("addCpuModal").style.display = "flex";
}
function closeAddCpuModal() {
    document.getElementById("addCpuModal").style.display = "none";
    document.getElementById("addCpuForm").reset();
}
function showAddRamModal() {
    document.getElementById("addRamModal").style.display = "flex";
}
function closeAddRamModal() {
    document.getElementById("addRamModal").style.display = "none";
    document.getElementById("addRamForm").reset();
}
function showToast(message, type) {
    const toast = document.createElement("div");
    toast.style.cssText = `position: fixed; top: 20px; right: 20px; background: ${type === "success" ? "#10b981" : "#ef4444"}; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1200; font-size: 14px; font-weight: 500;`;
    toast.textContent = message;
    document.body.appendChild(toast);
    setTimeout(() => { toast.remove(); }, 3000);
}
document.addEventListener("DOMContentLoaded", function() {
    document.getElementById("addCpuForm").addEventListener("submit", function(e) {
        e.preventDefault();
        const formData = {
            ajax_action: "add_cpu_model",
            name: document.getElementById("cpuName").value,
            manufacturer: document.getElementById("cpuManufacturer").value,
            cores: document.getElementById("cpuCores").value,
            frequency: document.getElementById("cpuFrequency").value,
            description: document.getElementById("cpuDescription").value
        };
        fetch(window.location.href, {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelectorAll(".cpu-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddCpuModal();
                showToast("CPU model added successfully!", "success");
            } else {
                showToast("Error adding CPU model: " + data.error, "error");
            }
        })
        .catch(error => {
            showToast("Network error: " + error.message, "error");
        });
    });
    document.getElementById("addRamForm").addEventListener("submit", function(e) {
        e.preventDefault();
        const formData = {
            ajax_action: "add_ram_config",
            name: document.getElementById("ramName").value,
            size: document.getElementById("ramSize").value,
            type: document.getElementById("ramType").value,
            speed: document.getElementById("ramSpeed").value,
            description: document.getElementById("ramDescription").value
        };
        fetch(window.location.href, {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelectorAll(".ram-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddRamModal();
                showToast("RAM configuration added successfully!", "success");
            } else {
                showToast("Error adding RAM configuration: " + data.error, "error");
            }
        })
        .catch(error => {
            showToast("Network error: " + error.message, "error");
        });
    });
});
</script>

?> 