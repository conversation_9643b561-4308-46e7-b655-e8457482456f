<?php

/**
 * DCIM Switch Management Module
 * 
 * This file contains all functions related to managing network switches in the DCIM system,
 * including switch creation, editing, deletion, bulk operations, and rack assignments.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, configurations)
 * - dcim-sidebar.php (navigation interface)
 * - dcim-locations.php (rack and location dependencies)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Import Capsule for database access
use Illuminate\Database\Capsule\Manager as Capsule;

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * Display switch listing table with management features
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_switches_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        $switches = Capsule::table('dcim_switches')
            ->leftJoin('dcim_racks', 'dcim_switches.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_switches.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_switches.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching switches: ' . $e->getMessage() . '</div>';
        $switches = array();
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Switches</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="window.location.href=\'' . $modulelink . '&action=switches_manage\'" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Switch</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Switches table
    if (count($switches) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Switch</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Type</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Ports</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Management IP</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($switches as $switch) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($switch->name) . '</div>';
            if ($switch->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($switch->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->location_name ? htmlspecialchars($switch->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->rack_name ? htmlspecialchars($switch->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($switch->start_unit) {
                echo 'U' . $switch->start_unit;
                if ($switch->unit_size > 1) {
                    echo '-' . ($switch->start_unit + $switch->unit_size - 1);
                }
                echo ' (' . $switch->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->switch_type ? htmlspecialchars(ucfirst($switch->switch_type)) : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->ports ? $switch->ports : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->management_ip ? htmlspecialchars($switch->management_ip) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$switch->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($switch->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editSwitch(' . $switch->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($switch->rack_id) {
                echo '<button onclick="viewInRack(' . $switch->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-network-wired"></i>';
        echo '<h3>No Switches Found</h3>';
        echo '<p>Add your first switch to get started</p>';
        echo '<button onclick="showAddSwitchModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Switch';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Switch functionality
    echo '<div id="addSwitchModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    dcim_generate_switches_javascript($modulelink);
}

/**
 * Main switch management interface with comprehensive features
 * 
 * Provides bulk switch addition, individual switch editing, device assignment
 * to racks, and advanced form validation with AJAX support.
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_manage_switches($modulelink) {
    // Debug logging
    error_log("DCIM: ===== dcim_manage_switches ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle bulk switch addition
    error_log("DCIM: Checking for bulk switch addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_switches') {
        error_log("DCIM: BULK SWITCH ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk switch addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values
        try {
            Capsule::schema()->table('dcim_switches', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated switches rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify switches rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['switches']) || !is_array($_POST['switches'])) {
            error_log("DCIM: No switches data found in POST");
            echo '<div style="color: red;">No switch data received. Please fill in at least one switch name.</div>';
            return;
        }
        
        foreach ($_POST['switches'] as $index => $switch_data) {
            // Skip empty entries (must have at least a name)
            if (empty(trim($switch_data['name'] ?? ''))) {
                continue;
            }
            
            $label = trim($switch_data['name']);
            error_log("DCIM: Processing switch: $label");
            
            try {
                Capsule::table('dcim_switches')->insert([
                    'name' => $label,
                    'hostname' => $switch_data['hostname'] ?? null,
                    'rack_id' => !empty($switch_data['rack_id']) ? $switch_data['rack_id'] : null,
                    'start_unit' => !empty($switch_data['start_unit']) ? $switch_data['start_unit'] : null,
                    'unit_size' => !empty($switch_data['unit_size']) ? $switch_data['unit_size'] : 1,
                    'make' => $switch_data['make'] ?? null,
                    'model' => $switch_data['model'] ?? null,
                    'serial_number' => $switch_data['serial_number'] ?? null,
                    'ports' => !empty($switch_data['ports']) ? $switch_data['ports'] : 24,
                    'switch_type' => $switch_data['switch_type'] ?? 'edge',
                    'management_ip' => $switch_data['management_ip'] ?? null,
                    'power_consumption' => $switch_data['power_consumption'] ?? null,
                    'client_id' => $switch_data['client_id'] ?? null,
                    'service_id' => $switch_data['service_id'] ?? null,
                    'notes' => $switch_data['notes'] ?? null,
                    'status' => $switch_data['status'] ?? 'online',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding switch " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count switches");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' switch(es) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' switches added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
                echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=switches_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' switch(es) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Get locations and racks for the interface
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
        $switch_models = Capsule::table('dcim_switch_models')->where('active', true)->get();
    } catch (Exception $e) {
        // Fallback data if database is not available
        $locations = array();
        $racks = array();
        $switch_models = array();
    }
    
    // Show the bulk addition interface
    if ($is_ajax) {
        // For AJAX requests, only return the modal content
        dcim_render_bulk_switch_interface($modulelink, $racks, $locations, $switch_models, true);
        return; // Stop execution here for AJAX requests
    } else {
        // For regular requests, render the full page
        dcim_render_bulk_switch_interface($modulelink, $racks, $locations, $switch_models, false);
    }
}

/**
 * Render bulk switch addition interface
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param array $switch_models Available switch models
 * @param bool $is_ajax Whether this is an AJAX request
 */
function dcim_render_bulk_switch_interface($modulelink, $racks, $locations, $switch_models, $is_ajax) {
    // Get switch models from database if not provided
    if (!$switch_models) {
        if (function_exists('dcim_get_switch_models')) {
            $switch_models = dcim_get_switch_models();
        } else {
            $switch_models = array();
        }
    }
    // Group racks by city
    $racksByCity = [];
    foreach ($racks as $rack) {
        $city = $rack->location_name ?? 'Unknown';
        if (!isset($racksByCity[$city])) {
            $racksByCity[$city] = [];
        }
        $racksByCity[$city][] = $rack;
    }
    // Modern modal-style container
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 1200px; width: 100%; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden;">';
    // Modal header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Bulk Add Switches</h2>';
    echo '<button type="button" onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>';
    echo '</div>';
    // Tab navigation
    echo '<div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">';
    echo '<ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">';
    $tabs = [
        'basic-info' => ['icon' => 'fa-folder', 'label' => 'Basic Info'],
        'location' => ['icon' => 'fa-map-marker-alt', 'label' => 'Location'],
        'snmp-settings' => ['icon' => 'fa-cog', 'label' => 'SNMP Settings']
    ];
    foreach ($tabs as $key => $tab) {
        $isActive = ($key === 'basic-info') ? 'active' : '';
        echo '<li role="presentation" class="' . $isActive . '" style="margin: 0; list-style: none;">';
        echo '<a href="#' . $key . '" aria-controls="' . $key . '" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: ' . ($isActive ? '#4f46e5' : '#6b7280') . '; font-weight: 500; text-decoration: none; border-bottom: 3px solid ' . ($isActive ? '#4f46e5' : 'transparent') . '; margin-bottom: -1px;">';
        echo '<i class="fas ' . $tab['icon'] . '" style="font-size: 14px;"></i>';
        echo $tab['label'];
        echo '</a>';
        echo '</li>';
    }
    echo '</ul>';
    echo '</div>';
    // Form
    $form_onsubmit = $is_ajax ? 'onsubmit="console.log(\'Switch form submit intercepted\'); return handleSwitchAjaxSubmit(this, event)"' : '';
    echo '<form method="post" id="bulkSwitchForm" ' . $form_onsubmit . ' style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    echo '<input type="hidden" name="action" value="bulk_add_switches" id="actionField">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1" id="ajaxField">';
    }
    // Tab content
    echo '<div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">';
    // Basic Info Tab
    echo '<div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    // Table container with scroll
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">Label*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">IP Address*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 150px;">Password*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 180px;">Model*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 120px;">Status</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 80px;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    for ($i = 1; $i <= 9; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][label]" class="form-control switch-label-input" placeholder="Switch Label" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="position: relative;">';
        echo '<select name="switches[' . ($i-1) . '][ip_address]" class="form-control switch-ip-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: white;">';
        echo '<option value="">Select Switch IP address</option>';
        echo '</select>';
        echo '<div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280; pointer-events: none;"><i class="fas fa-refresh"></i></div>';
        echo '</div>';
        echo '<div style="color: #f59e0b; font-size: 12px; margin-top: 4px;">Select a city first</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="password" name="switches[' . ($i-1) . '][password]" class="form-control" placeholder="Password" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="switches[' . ($i-1) . '][model]" class="form-control switch-model-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select Switch Model</option>';
        foreach ($switch_models as $model) {
            echo '<option value="' . htmlspecialchars($model->name) . '">' . htmlspecialchars($model->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddSwitchModelModal()" title="Add Switch Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="available">Available</option>';
        echo '<option value="in_use">In Use</option>';
        echo '<option value="maintenance">Maintenance</option>';
        echo '<option value="offline">Offline</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    // Location Tab
    echo '<div role="tabpanel" class="tab-pane" id="location" style="flex: 1; flex-direction: column; overflow: hidden; display: none;">';
    // Top action buttons for location
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">City*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Rack*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Top Position</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="switch-label-display">Device ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][city]" class="form-control city-select" onchange="updateSwitchRackOptions(' . ($i-1) . ')" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select City</option>';
        foreach ($racksByCity as $city => $cityRacks) {
            echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Rack</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][top_position]" class="form-control" placeholder="e.g. 38" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    // SNMP Settings Tab
    echo '<div role="tabpanel" class="tab-pane" id="snmp-settings" style="flex: 1; flex-direction: column; overflow: hidden; display: none;">';
    // Top action buttons for SNMP
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SNMP Community</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SNMP Version</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="switch-label-display">Switch ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="position: relative;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][snmp_community]" class="form-control" placeholder="Private Community String" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 32px 8px 12px; font-size: 14px; width: 100%;">';
        echo '<div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280; pointer-events: none;"><i class="fas fa-lock"></i></div>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][snmp_version]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="2c">Version 2c</option>';
        echo '<option value="1">Version 1</option>';
        echo '<option value="3">Version 3</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>'; // End tab-content
    // Modal footer
    echo '<div style="padding: 20px 32px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">';
    echo '<span style="color: #6b7280; font-size: 14px;"><span id="switchCount">0</span> Switches configured</span>';
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="button" class="btn" onclick="closeModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" class="btn" disabled style="background: #5b21b6; color: white; border: none; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px; opacity: 0.6;">';
    echo '<i class="fas fa-plus-circle"></i> Add 0 Switches';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    echo '</form>';
    echo '</div>'; // End modal content
    echo '</div>'; // End modal container
    // Add CSS for tab functionality
    echo '<style>/* ... CSS omitted for brevity ... */</style>';
    // Enhanced JavaScript for switches
    echo '<script>
    // Global variables for switches
    let currentSwitchTabIndex = 0;
    const maxSwitches = 10;
    const isSwitchAjaxModal = ' . ($is_ajax ? 'true' : 'false') . ';

    // Available racks by city for switches
    const switchRacksByCity = ' . json_encode($racksByCity) . ';

    // Handle AJAX form submission for switches
    function handleSwitchAjaxSubmit(form, event) {
        console.log("handleSwitchAjaxSubmit called, isSwitchAjaxModal:", isSwitchAjaxModal);

        if (!isSwitchAjaxModal) {
            return true; // Allow normal submission
        }

        // Prevent normal form submission
        if (event) {
            event.preventDefault();
        }

        console.log("Preventing default submission, starting switch AJAX submission");

        // Validate form before submission
        let hasValidSwitch = false;
        let filledCount = 0;

        const switchLabelInputs = document.querySelectorAll(".switch-label-input");
        switchLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                hasValidSwitch = true;
                filledCount++;
            }
        });

        if (!hasValidSwitch) {
            alert("Please fill in at least one switch label before submitting.");
            return false;
        }

        console.log("Switch form validation passed, " + filledCount + " switches to add");

        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding " + filledCount + " Switches...";
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);

        console.log("Switch form data being submitted");

        // Submit via AJAX
        const ajaxUrl = "' . $modulelink . '&action=switches_manage&ajax=1";
        console.log("Switch AJAX URL:", ajaxUrl);

        fetch(ajaxUrl, {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            console.log("Switch AJAX response received");

            const isSuccess = html.includes("switches added successfully") ||
                             html.includes("ajaxSuccessIndicator") ||
                             html.includes("SUCCESS:");

            if (isSuccess) {
                console.log("Switch success detected in response");
                const modalContainer = document.getElementById("addSwitchModalContainer");
                modalContainer.innerHTML = html;

                setTimeout(function() {
                    closeModal();
                    window.location.reload();
                }, 2000);
            } else {
                console.log("Switch submission completed but no clear success indicator");
                const modalContainer = document.getElementById("addSwitchModalContainer");
                if (modalContainer) {
                    modalContainer.innerHTML = html;

                    const scripts = modalContainer.querySelectorAll("script");
                    scripts.forEach(script => {
                        try {
                            eval(script.textContent);
                        } catch (e) {
                            console.error("Error executing script:", e);
                        }
                    });

                    if (typeof initializeSwitchAjaxModal === "function") {
                        initializeSwitchAjaxModal();
                    }
                }
            }
        })
        .catch(error => {
            console.error("Switch AJAX submission error:", error);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            alert("Error submitting form. Please try again.");
        });

        return false;
    }

    // Tab functionality
    (function() {
        var tabLinks = document.querySelectorAll(".nav-tabs a");
        var tabPanes = document.querySelectorAll(".tab-pane");
        tabLinks.forEach(function(link) {
            link.addEventListener("click", function(e) {
                e.preventDefault();
                tabLinks.forEach(function(l) { l.parentElement.classList.remove("active"); });
                tabPanes.forEach(function(p) { p.classList.remove("active"); });
                this.parentElement.classList.add("active");
                var target = this.getAttribute("href").replace("#", "");
                var pane = document.getElementById(target);
                if (!pane) pane = document.querySelector(".tab-pane#" + target);
                if (!pane) pane = document.querySelector(".tab-pane[id=\'" + target + "\']");
                if (!pane) pane = document.querySelector(".tab-pane." + target);
                if (pane) pane.classList.add("active");
            });
        });
        if (tabLinks.length > 0) {
            tabLinks[0].click();
        }
    })();

    // Switch-specific functions
    function addSwitchRow() {
        console.log("addSwitchRow called");
        // Implementation would add dynamic rows
        updateSwitchCount();
    }

    function addFiveSwitchRows() {
        console.log("addFiveSwitchRows called");
        // Implementation would add 5 dynamic rows
        updateSwitchCount();
    }

    function removeSwitchRow(button) {
        console.log("removeSwitchRow called");
        const row = button.closest("tr");
        if (row) {
            row.remove();
            updateSwitchCount();
        }
    }

    function updateSwitchCount() {
        let count = 0;
        const switchLabelInputs = document.querySelectorAll(".switch-label-input");

        switchLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                count++;
            }
        });

        // Update counter display
        const counter = document.getElementById("switchCount");
        if (counter) {
            counter.textContent = count;
        }

        // Update submit button
        const submitBtn = document.querySelector("button[type=submit]");
        if (submitBtn) {
            const submitCount = document.getElementById("submitSwitchCount") || submitBtn.querySelector("span");
            if (submitCount) {
                submitCount.textContent = count;
            }

            if (count > 0) {
                submitBtn.disabled = false;
                submitBtn.style.opacity = "1";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add " + count + " Switches";
            } else {
                submitBtn.disabled = true;
                submitBtn.style.opacity = "0.6";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add 0 Switches";
            }
        }

        console.log("Switch count updated to:", count);
    }

    // Switch Model Modal Functions
    function showAddSwitchModelModal() {
        const modalHtml = `
            <div id="addSwitchModelModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1060; display: flex; align-items: center; justify-content: center; padding: 20px;">
                <div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 500px; width: 100%; max-height: 90vh; overflow: hidden;">
                    <div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add Switch Model</h3>
                        <button type="button" onclick="closeAddSwitchModelModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>
                    </div>
                    <form id="addSwitchModelForm" style="padding: 24px 32px;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Model Name *</label>
                            <input type="text" id="switchModelName" placeholder="e.g., Cisco Catalyst 2960" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Manufacturer</label>
                            <input type="text" id="switchModelManufacturer" placeholder="e.g., Cisco" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">
                            <button type="button" onclick="closeAddSwitchModelModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; cursor: pointer; font-size: 14px;">Cancel</button>
                            <button type="submit" style="padding: 8px 16px; border: none; border-radius: 6px; background: #5b21b6; color: white; cursor: pointer; font-size: 14px;">Add Model</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML("beforeend", modalHtml);

        // Add form submission handler
        document.getElementById("addSwitchModelForm").addEventListener("submit", function(e) {
            e.preventDefault();
            const formData = {
                ajax_action: "add_switch_model",
                name: document.getElementById("switchModelName").value,
                manufacturer: document.getElementById("switchModelManufacturer").value
            };
            addSwitchModel(formData);
        });
    }

    function closeAddSwitchModelModal() {
        const modal = document.getElementById("addSwitchModelModal");
        if (modal) {
            modal.remove();
        }
    }

    function addSwitchModel(formData) {
        // This would typically make an AJAX call to add the switch model
        console.log("Adding switch model:", formData);
        closeAddSwitchModelModal();
    }

    // Make functions globally available
    window.handleSwitchAjaxSubmit = handleSwitchAjaxSubmit;
    window.addSwitchRow = addSwitchRow;
    window.addFiveSwitchRows = addFiveSwitchRows;
    window.removeSwitchRow = removeSwitchRow;
    window.updateSwitchCount = updateSwitchCount;
    window.showAddSwitchModelModal = showAddSwitchModelModal;
    window.closeAddSwitchModelModal = closeAddSwitchModelModal;

    // Initialize switch AJAX modal functionality
    window.initializeSwitchAjaxModal = function() {
        // Re-assign functions to ensure they work in AJAX context
        window.handleSwitchAjaxSubmit = handleSwitchAjaxSubmit;
        window.addSwitchRow = addSwitchRow;
        window.addFiveSwitchRows = addFiveSwitchRows;
        window.removeSwitchRow = removeSwitchRow;
        window.updateSwitchCount = updateSwitchCount;
        window.showAddSwitchModelModal = showAddSwitchModelModal;
        window.closeAddSwitchModelModal = closeAddSwitchModelModal;
        console.log("Switch AJAX modal initialized - functions are now available");
    };
    </script>';
}

/**
 * Render the switch addition form
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations  
 * @param array $switch_models Available switch models
 * @param bool $is_ajax Whether this is an AJAX request
 */
function dcim_render_switch_form($modulelink, $racks, $locations, $switch_models, $is_ajax) {
    echo '<form id="bulkSwitchForm" method="post" onsubmit="return handleSwitchSubmit(this, event);">';
    echo '<input type="hidden" name="action" value="bulk_add_switches">';
    
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 24px; margin-bottom: 24px;">';
    echo '<div style="display: flex; justify-content: between; align-items: center; margin-bottom: 16px;">';
    echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Switch Configuration</h3>';
    echo '<span id="switchCounter" style="background: #f3f4f6; color: #374151; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600;">0 switches ready</span>';
    echo '</div>';
    
    echo '<div style="overflow-x: auto;">';
    echo '<table class="table" style="margin: 0;">';
    echo '<thead>';
    echo '<tr>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">#</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Switch Name *</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Type</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Ports</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Management IP</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Location</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Rack</th>';
    echo '<th style="padding: 12px; text-align: left; font-weight: 600; color: #374151; background: #f8fafc;">Unit</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    // Group racks by city
    $racksByCity = [];
    foreach ($racks as $rack) {
        $city = $rack->location_name ?? 'Unknown';
        if (!isset($racksByCity[$city])) {
            $racksByCity[$city] = [];
        }
        $racksByCity[$city][] = $rack;
    }
    
    // Generate 10 switch input rows
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 12px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 12px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][name]" class="form-control switch-name-input" placeholder="e.g., SW-CORE-01" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;" onchange="updateSwitchCounter()">';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<select name="switches[' . ($i-1) . '][switch_type]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Type</option>';
        echo '<option value="core">Core Switch</option>';
        echo '<option value="edge">Edge Switch</option>';
        echo '<option value="tor">Top of Rack</option>';
        echo '<option value="management">Management</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<input type="number" name="switches[' . ($i-1) . '][ports]" class="form-control" placeholder="24" value="24" min="1" max="128" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][management_ip]" class="form-control" placeholder="************" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<select name="switches[' . ($i-1) . '][city]" class="form-control city-select" onchange="updateSwitchRackOptions(' . ($i-1) . ')" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select City</option>';
        foreach ($racksByCity as $city => $cityRacks) {
            echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<select name="switches[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Rack</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 12px;">';
        echo '<input type="number" name="switches[' . ($i-1) . '][start_unit]" class="form-control" placeholder="42" min="1" max="42" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="submit" id="submitSwitchesBtn" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Add Switches';
    echo '</button>';
    if (!$is_ajax) {
        echo '<button type="button" onclick="goBack()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">';
        echo '<i class="fas fa-times"></i> Cancel';
        echo '</button>';
    }
    echo '</div>';
    
    echo '</form>';
    
    // Add JavaScript for form functionality
    echo '<script>
    const switchRacksByCity = ' . json_encode($racksByCity) . ';
    
    function updateSwitchRackOptions(switchIndex) {
        const citySelect = document.querySelector("select[name=\"switches[" + switchIndex + "][city]\"]");
        const rackSelect = document.querySelector("select[name=\"switches[" + switchIndex + "][rack_id]\"]");
        
        if (!citySelect || !rackSelect) return;
        
        const selectedCity = citySelect.value;
        rackSelect.innerHTML = "<option value=\"\">Select Rack</option>";
        
        if (selectedCity && switchRacksByCity[selectedCity]) {
            switchRacksByCity[selectedCity].forEach(function(rack) {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.name + " (" + rack.units + " units)";
                rackSelect.appendChild(option);
            });
        }
    }
    
    function updateSwitchCounter() {
        let count = 0;
        document.querySelectorAll(".switch-name-input").forEach(input => {
            if (input.value.trim()) count++;
        });
        
        const counter = document.getElementById("switchCounter");
        if (counter) {
            counter.textContent = count + " switches ready";
            counter.style.background = count > 0 ? "#10b981" : "#f3f4f6";
            counter.style.color = count > 0 ? "white" : "#374151";
        }
    }
    
    function handleSwitchSubmit(form, event) {
        if (' . ($is_ajax ? 'true' : 'false') . ') {
            event.preventDefault();
            
            const formData = new FormData(form);
            const submitBtn = document.getElementById("submitSwitchesBtn");
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding Switches...";
            submitBtn.disabled = true;
            
            fetch("' . $modulelink . '&action=switches_manage&ajax=1", {
                method: "POST",
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Check for success indicator
                if (html.includes("ajaxSuccessIndicator")) {
                    const tempDiv = document.createElement("div");
                    tempDiv.innerHTML = html;
                    document.body.appendChild(tempDiv);
                    
                    setTimeout(() => {
                        if (typeof closeModal === "function") {
                            closeModal();
                        }
                        window.location.reload();
                    }, 2000);
                } else {
                    // Show error or other response
                    document.body.insertAdjacentHTML("beforeend", html);
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error("Switch form submission error:", error);
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                alert("Error submitting form. Please try again.");
            });
            
            return false;
        }
        
        return true; // Allow normal submission for non-AJAX
    }
    </script>';
}

/**
 * Generate JavaScript for switch management functionality
 * 
 * @param string $modulelink The WHMCS module link
 */
function dcim_generate_switches_javascript($modulelink) {
    echo '<script>
    function editSwitch(switchId) {
        window.location.href = "' . $modulelink . '&action=switches&edit=" + switchId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddSwitchModal() {
        // Show loading state
        const modalContainer = document.getElementById("addSwitchModalContainer");
        modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"font-size: 24px; color: #4299e1; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #6b7280;\">Loading switch configuration...</p></div></div>";
        modalContainer.style.display = "block";

        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=switches_manage&ajax=1")
            .then(response => response.text())
            .then(html => {
                modalContainer.innerHTML = html;

                // Execute any scripts in the loaded content
                const scripts = modalContainer.querySelectorAll("script");
                scripts.forEach(script => {
                    try {
                        eval(script.textContent);
                    } catch (e) {
                        console.error("Error executing script:", e);
                    }
                });

                // Initialize modal content after scripts are loaded
                setTimeout(function() {
                    try {
                        // Force initialization of all modal functions
                        if (typeof initializeSwitchAjaxModal === "function") {
                            initializeSwitchAjaxModal();
                        }
                    } catch (error) {
                        console.log("Could not initialize switch modal content:", error);
                    }
                }, 200);
            })
            .catch(error => {
                console.error("Error loading modal:", error);
                modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-exclamation-triangle\" style=\"font-size: 24px; color: #ef4444; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #374151;\">Error loading switch configuration</p><button onclick=\"closeModal()\" style=\"margin-top: 16px; background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;\">Close</button></div></div>";
            });
    }
    
    function closeModal() {
        const modalContainer = document.getElementById("addSwitchModalContainer");
        modalContainer.style.display = "none";
        modalContainer.innerHTML = "";
    }
    
    function goBack() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    </script>';
}

/**
 * Get switch statistics for dashboard
 * 
 * @return array Switch statistics
 */
function dcim_get_switch_stats() {
    try {
        $total_switches = Capsule::table('dcim_switches')->count();
        $online_switches = Capsule::table('dcim_switches')->where('status', 'online')->count();
        $assigned_switches = Capsule::table('dcim_switches')->whereNotNull('rack_id')->count();
        $unassigned_switches = Capsule::table('dcim_switches')->whereNull('rack_id')->count();
        
        $core_switches = Capsule::table('dcim_switches')->where('switch_type', 'core')->count();
        $edge_switches = Capsule::table('dcim_switches')->where('switch_type', 'edge')->count();
        $tor_switches = Capsule::table('dcim_switches')->where('switch_type', 'tor')->count();
        
        return [
            'total' => $total_switches,
            'online' => $online_switches,
            'assigned' => $assigned_switches,
            'unassigned' => $unassigned_switches,
            'core' => $core_switches,
            'edge' => $edge_switches,
            'tor' => $tor_switches,
            'online_percentage' => $total_switches > 0 ? round(($online_switches / $total_switches) * 100, 1) : 0,
            'assignment_percentage' => $total_switches > 0 ? round(($assigned_switches / $total_switches) * 100, 1) : 0
        ];
    } catch (Exception $e) {
        error_log("DCIM: Error getting switch stats - " . $e->getMessage());
        return [
            'total' => 0,
            'online' => 0,
            'assigned' => 0,
            'unassigned' => 0,
            'core' => 0,
            'edge' => 0,
            'tor' => 0,
            'online_percentage' => 0,
            'assignment_percentage' => 0
        ];
    }
}

/**
 * Get all switches for a specific rack
 * 
 * @param int $rack_id The rack ID
 * @return array Array of switch objects
 */
function dcim_get_rack_switches($rack_id) {
    try {
        return Capsule::table('dcim_switches')
            ->where('rack_id', $rack_id)
            ->orderBy('start_unit', 'desc')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching rack switches - " . $e->getMessage());
        return array();
    }
}

/**
 * Validate switch data
 * 
 * @param array $data Switch data to validate
 * @return array Array with 'valid' boolean and 'errors' array
 */
function dcim_validate_switch_data($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors[] = 'Switch name is required';
    }
    
    if (!empty($data['start_unit']) && (!is_numeric($data['start_unit']) || $data['start_unit'] < 1 || $data['start_unit'] > 42)) {
        $errors[] = 'Start unit must be between 1 and 42';
    }
    
    if (!empty($data['unit_size']) && (!is_numeric($data['unit_size']) || $data['unit_size'] < 1 || $data['unit_size'] > 8)) {
        $errors[] = 'Unit size must be between 1 and 8';
    }
    
    if (!empty($data['ports']) && (!is_numeric($data['ports']) || $data['ports'] < 1 || $data['ports'] > 128)) {
        $errors[] = 'Port count must be between 1 and 128';
    }
    
    if (!empty($data['management_ip']) && !filter_var($data['management_ip'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid management IP address format';
    }
    
    if (!empty($data['switch_type']) && !in_array($data['switch_type'], ['core', 'edge', 'tor', 'management'])) {
        $errors[] = 'Invalid switch type';
    }
    
    return [
        'valid' => count($errors) == 0,
        'errors' => $errors
    ];
}

/**
 * Create a new switch
 * 
 * @param array $data Switch data
 * @return array Result array with success/error information
 */
function dcim_create_switch($data) {
    try {
        $validation = dcim_validate_switch_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $switch_id = Capsule::table('dcim_switches')->insertGetId([
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 1,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'ports' => $data['ports'] ?? 24,
            'switch_type' => $data['switch_type'] ?? 'edge',
            'management_ip' => $data['management_ip'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'offline',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        return ['success' => true, 'switch_id' => $switch_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating switch - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update an existing switch
 * 
 * @param int $switch_id The switch ID
 * @param array $data Updated switch data
 * @return array Result array with success/error information
 */
function dcim_update_switch($switch_id, $data) {
    try {
        $validation = dcim_validate_switch_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $updateData = [
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 1,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'ports' => $data['ports'] ?? 24,
            'switch_type' => $data['switch_type'] ?? 'edge',
            'management_ip' => $data['management_ip'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        Capsule::table('dcim_switches')
            ->where('id', $switch_id)
            ->update($updateData);
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error updating switch - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Delete a switch
 * 
 * @param int $switch_id The switch ID to delete
 * @return array Result array with success/error information
 */
function dcim_delete_switch($switch_id) {
    try {
        // Also clean up any IP assignments
        $ip_assignments = Capsule::table('dcim_ip_assignments')
            ->where('device_type', 'switch')
            ->where('device_id', $switch_id)
            ->get();
            
        foreach ($ip_assignments as $assignment) {
            // Mark IP as available again
            Capsule::table('dcim_ip_addresses')
                ->where('id', $assignment->ip_address_id)
                ->update(['status' => 'available', 'updated_at' => date('Y-m-d H:i:s')]);
                
            // Delete assignment
            Capsule::table('dcim_ip_assignments')->where('id', $assignment->id)->delete();
        }
        
        // Delete the switch
        Capsule::table('dcim_switches')->where('id', $switch_id)->delete();
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting switch - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get all switch types
 * 
 * @return array Array of switch types
 */
function dcim_get_switch_types() {
    return [
        'core' => 'Core Switch',
        'edge' => 'Edge Switch', 
        'tor' => 'Top of Rack',
        'management' => 'Management Switch'
    ];
}

/**
 * Switch management functions
 * Note: dcim_get_switch_models() is defined in dcim-core.php to avoid conflicts
 */

?> 