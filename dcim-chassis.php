<?php

/**
 * DCIM Chassis Management Module
 * 
 * This file contains all functions related to managing blade chassis in the DCIM system,
 * including chassis creation, editing, deletion, bulk operations, and rack assignments.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, configurations)
 * - dcim-sidebar.php (navigation interface)
 * - dcim-locations.php (rack and location dependencies)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * Display chassis listing table with management features
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_chassies_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();

    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';

    // Add fallback CSS for Font Awesome icons
    echo '<style>
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-edit::before { content: "✎"; }
    .fas.fa-eye::before { content: "👁"; }
    .fas.fa-trash::before { content: "🗑"; }
    .fas.fa-hdd::before { content: "💾"; }
    .fas.fa-server::before { content: "🖥"; }
    .fas.fa-network-wired::before { content: "🔗"; }
    .fas.fa-map-marker-alt::before { content: "📍"; }
    .fas.fa-building::before { content: "🏢"; }
    .fas.fa-cog::before { content: "⚙"; }
    .fas.fa-globe::before { content: "◍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>';

    try {
        $chassies = Capsule::table('dcim_chassies')
            ->leftJoin('dcim_racks', 'dcim_chassies.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_chassies.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_chassies.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching chassies: ' . $e->getMessage() . '</div>';
        $chassies = collect([]);
    }

    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';

    // Use shared sidebar
    dcim_generate_sidebar($modulelink);

    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Chassis</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="showAddChassisModal()" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Chassis</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Chassies table
    if (count($chassies) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Chassis</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Type</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Slots</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Management IP</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($chassies as $chassis) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($chassis->name) . '</div>';
            if ($chassis->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($chassis->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->location_name ? htmlspecialchars($chassis->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->rack_name ? htmlspecialchars($chassis->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($chassis->start_unit) {
                echo 'U' . $chassis->start_unit;
                if ($chassis->unit_size > 1) {
                    echo '-' . ($chassis->start_unit + $chassis->unit_size - 1);
                }
                echo ' (' . $chassis->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->chassis_type ? htmlspecialchars(ucfirst($chassis->chassis_type)) : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->slots ? $chassis->slots : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->management_ip ? htmlspecialchars($chassis->management_ip) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$chassis->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($chassis->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editChassis(' . $chassis->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($chassis->rack_id) {
                echo '<button onclick="viewInRack(' . $chassis->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-hdd"></i>';
        echo '<h3>No Chassies Found</h3>';
        echo '<p>Add your first chassis to get started</p>';
        echo '<button onclick="showAddChassisModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Chassis';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Chassis functionality
    echo '<div id="addChassisModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    dcim_generate_chassis_javascript($modulelink);
}

/**
 * Main chassis management interface with comprehensive features
 * 
 * Provides bulk chassis addition, individual chassis editing, device assignment
 * to racks, and advanced form validation with AJAX support.
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_manage_chassies($modulelink) {
    // Debug logging
    error_log("DCIM: ===== dcim_manage_chassies ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle bulk chassis addition
    error_log("DCIM: Checking for bulk chassis addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_chassies') {
        error_log("DCIM: BULK CHASSIS ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk chassis addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values
        try {
            Capsule::schema()->table('dcim_chassies', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated chassies rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify chassies rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['chassies']) || !is_array($_POST['chassies'])) {
            error_log("DCIM: No chassies data found in POST");
            echo '<div style="color: red;">No chassis data received. Please fill in at least one chassis label.</div>';
            return;
        }
        
        foreach ($_POST['chassies'] as $index => $chassis_data) {
            // Skip empty entries (must have at least a label)
            if (empty(trim($chassis_data['label'] ?? ''))) {
                continue;
            }
            
            $label = trim($chassis_data['label']);
            error_log("DCIM: Processing chassis: $label");
            
            try {
                Capsule::table('dcim_chassies')->insert([
                    'name' => $label,
                    'hostname' => $chassis_data['hostname'] ?? null,
                    'rack_id' => !empty($chassis_data['rack_id']) ? $chassis_data['rack_id'] : null,
                    'start_unit' => !empty($chassis_data['start_unit']) ? $chassis_data['start_unit'] : null,
                    'unit_size' => !empty($chassis_data['unit_size']) ? $chassis_data['unit_size'] : 10,
                    'make' => $chassis_data['model'] ?? null,
                    'model' => $chassis_data['model'] ?? null,
                    'serial_number' => $chassis_data['serial_number'] ?? null,
                    'slots' => !empty($chassis_data['slots']) ? $chassis_data['slots'] : 8,
                    'chassis_type' => $chassis_data['chassis_type'] ?? 'blade',
                    'management_ip' => $chassis_data['management_ip'] ?? null,
                    'power_consumption' => $chassis_data['power_consumption'] ?? 0,
                    'client_id' => $chassis_data['client_id'] ?? null,
                    'service_id' => $chassis_data['service_id'] ?? null,
                    'notes' => $chassis_data['notes'] ?? null,
                    'status' => $chassis_data['status'] ?? 'offline',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding chassis " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count chassies");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' chassis(es) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' chassies added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
                echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=chassies_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' chassis(es) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Get locations and racks for the interface
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
        $chassis_models = Capsule::table('dcim_chassis_models')->where('active', true)->get();
    } catch (Exception $e) {
        // Fallback data if database is not available
        $locations = collect([]);
        $racks = collect([]);
        $chassis_models = collect([]);
    }
    
    // Show the bulk addition interface
    if ($is_ajax) {
        // For AJAX requests, only return the modal content
        dcim_render_bulk_chassis_interface($modulelink, $racks, $locations, $chassis_models, true);
        return; // Stop execution here for AJAX requests
    } else {
        // For regular requests, render the full page
        dcim_render_bulk_chassis_interface($modulelink, $racks, $locations, $chassis_models, false);
    }
}

/**
 * Render bulk chassis addition interface
 * 
 * @param string $modulelink The WHMCS module link
 * @param array $racks Available racks
 * @param array $locations Available locations
 * @param array $chassis_models Available chassis models
 * @param bool $is_ajax Whether this is an AJAX request
 */
function dcim_render_bulk_chassis_interface($modulelink, $racks, $locations, $chassis_models, $is_ajax) {
    // Get chassis models from database if not provided
    if (!$chassis_models) {
        if (function_exists('dcim_get_chassis_models')) {
            $chassis_models = dcim_get_chassis_models();
        } else {
            $chassis_models = array();
        }
    }

    // Group racks by city
    $racksByCity = [];
    foreach ($racks as $rack) {
        $city = $rack->location_name ?? 'Unknown';
        if (!isset($racksByCity[$city])) {
            $racksByCity[$city] = [];
        }
        $racksByCity[$city][] = $rack;
    }

    // Define JavaScript functions early so they're available when HTML is rendered
    echo '<script>
    // Chassis model modal functions - defined immediately for global access
    function showAddChassisModelModal() {
        const modal = document.getElementById("addChassisModelModal");
        if (modal) {
            modal.style.display = "flex";
        } else {
            console.error("Chassis modal not found!");
        }
    }

    function closeAddChassisModelModal() {
        const modal = document.getElementById("addChassisModelModal");
        if (modal) {
            modal.style.display = "none";
            document.getElementById("addChassisModelForm").reset();
        }
    }

    function addChassisModel(formData) {
        fetch("' . $modulelink . '", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add new option to all chassis model selects
                document.querySelectorAll("select[name*=\"[model]\"]").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddChassisModelModal();
                if (typeof showToast === "function") {
                    showToast("Chassis model added successfully!", "success");
                }
            } else {
                if (typeof showToast === "function") {
                    showToast("Error adding chassis model: " + (data.error || "Unknown error"), "error");
                } else {
                    alert("Error adding chassis model: " + (data.error || "Unknown error"));
                }
            }
        });
    }

    // Make functions globally available immediately
    window.showAddChassisModelModal = showAddChassisModelModal;
    window.closeAddChassisModelModal = closeAddChassisModelModal;
    window.addChassisModel = addChassisModel;

    // Also define closeModal for compatibility with existing buttons
    window.closeModal = function() {
        // Try to close any open modal
        const modals = document.querySelectorAll(\'[id*="Modal"]\');
        modals.forEach(modal => {
            if (modal.style.display === "flex" || modal.style.display === "block") {
                modal.style.display = "none";
            }
        });

        // Close the main modal container
        const container = document.querySelector(".dcim-modal-overlay");
        if (container && container.parentElement) {
            container.parentElement.style.display = "none";
        }

        // Also close the AJAX modal container
        const ajaxContainer = document.getElementById("addChassisModalContainer");
        if (ajaxContainer) {
            ajaxContainer.style.display = "none";
        }
    };

    // Initialize chassis AJAX modal functionality
    window.initializeChassisAjaxModal = function() {
        // Re-assign functions to ensure they work in AJAX context
        window.showAddChassisModelModal = showAddChassisModelModal;
        window.closeAddChassisModelModal = closeAddChassisModelModal;
        window.addChassisModel = addChassisModel;
        console.log("Chassis AJAX modal initialized - functions are now available");
    };
    </script>';

    // Modern modal-style container
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 1200px; width: 100%; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden;">';

    // Modal header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Bulk Add Chassies</h2>';
    echo '<button type="button" onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>';
    echo '</div>';

    // Tab navigation
    echo '<div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">';
    echo '<ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">';
    $tabs = [
        'basic-info' => ['icon' => 'fa-folder', 'label' => 'Basic Info'],
        'location' => ['icon' => 'fa-map-marker-alt', 'label' => 'Location'],
        'specifications' => ['icon' => 'fa-cog', 'label' => 'Specifications']
    ];
    foreach ($tabs as $key => $tab) {
        $isActive = ($key === 'basic-info') ? 'active' : '';
        echo '<li role="presentation" class="' . $isActive . '" style="margin: 0; list-style: none;">';
        echo '<a href="#' . $key . '" aria-controls="' . $key . '" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: ' . ($isActive ? '#4f46e5' : '#6b7280') . '; font-weight: 500; text-decoration: none; border-bottom: 3px solid ' . ($isActive ? '#4f46e5' : 'transparent') . '; margin-bottom: -1px;">';
        echo '<i class="fas ' . $tab['icon'] . '" style="font-size: 14px;"></i>';
        echo $tab['label'];
        echo '</a>';
        echo '</li>';
    }

    echo '</ul>';
    echo '</div>';

    // Form
    $form_onsubmit = $is_ajax ? 'onsubmit="console.log(\'Chassis form submit intercepted\'); return handleChassisAjaxSubmit(this, event)"' : '';
    echo '<form method="post" id="bulkChassisForm" ' . $form_onsubmit . ' style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    echo '<input type="hidden" name="action" value="bulk_add_chassies" id="actionField">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1" id="ajaxField">';
    }

    // Tab content
    echo '<div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">';

    // Basic Info Tab
    echo '<div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addChassisRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveChassisRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';

    // Table container with scroll
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">Label*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">Management IP</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 180px;">Model*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 120px;">Type</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 100px;">Slots</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 80px;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    for ($i = 1; $i <= 5; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="chassies[' . ($i-1) . '][label]" class="form-control chassis-label-input" placeholder="Chassis Label" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="chassies[' . ($i-1) . '][management_ip]" class="form-control" placeholder="*************" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="chassies[' . ($i-1) . '][model]" class="form-control chassis-model-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select Chassis Model</option>';
        foreach ($chassis_models as $model) {
            echo '<option value="' . htmlspecialchars($model->name) . '">' . htmlspecialchars($model->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddChassisModelModal()" title="Add Chassis Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][chassis_type]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="blade">Blade</option>';
        echo '<option value="storage">Storage</option>';
        echo '<option value="network">Network</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="number" name="chassies[' . ($i-1) . '][slots]" class="form-control" placeholder="8" value="8" min="1" max="32" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeChassisRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';

    // Location Tab
    echo '<div role="tabpanel" class="tab-pane" id="location" style="flex: 1; flex-direction: column; overflow: hidden; display: none;">';
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addChassisRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '</div>';
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">City*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Rack*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Top Position</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Unit Size</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    for ($i = 1; $i <= 5; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="chassis-label-display">Chassis ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][city]" class="form-control city-select" onchange="updateChassisRackOptions(' . ($i-1) . ')" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select City</option>';
        foreach ($racksByCity as $city => $cityRacks) {
            echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Rack</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="chassies[' . ($i-1) . '][start_unit]" class="form-control" placeholder="e.g. 30" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="number" name="chassies[' . ($i-1) . '][unit_size]" class="form-control" placeholder="10" value="10" min="1" max="20" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeChassisRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';

    echo '</div>'; // End tab-content

    // Modal footer
    echo '<div style="padding: 20px 32px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">';
    echo '<span style="color: #6b7280; font-size: 14px;"><span id="chassisCount">0</span> Chassis configured</span>';
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="button" class="btn" onclick="closeModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" class="btn" disabled style="background: #5b21b6; color: white; border: none; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px; opacity: 0.6;">';
    echo '<i class="fas fa-plus-circle"></i> Add 0 Chassis';
    echo '</button>';
    echo '</div>';
    echo '</div>';

    echo '</form>';
    echo '</div>';
    echo '</div>';

    // Add Chassis Model Modal
    echo '<div id="addChassisModelModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add Chassis Model</h3>';
    echo '<button type="button" onclick="closeAddChassisModelModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addChassisModelForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Model Name *</label>';
    echo '<input type="text" id="chassisModelName" placeholder="e.g., Dell PowerEdge M1000e" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Manufacturer</label>';
    echo '<input type="text" id="chassisModelManufacturer" placeholder="e.g., Dell" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="chassisModelDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddChassisModelModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; cursor: pointer; font-size: 14px;">Cancel</button>';
    echo '<button type="submit" style="padding: 8px 16px; border: none; border-radius: 6px; background: #5b21b6; color: white; cursor: pointer; font-size: 14px;">Add Model</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';

    // Add CSS for tab functionality
    echo '<style>
    .nav-tabs .active a {
        color: #4f46e5 !important;
        border-bottom-color: #4f46e5 !important;
    }
    .tab-pane {
        display: none !important;
    }
    .tab-pane.active {
        display: flex !important;
    }
    </style>';

    // Enhanced JavaScript for chassis
    echo '<script>
    // Global variables for chassis
    let currentChassisTabIndex = 0;
    const maxChassis = 10;
    const isChassisAjaxModal = ' . ($is_ajax ? 'true' : 'false') . ';

    // Available racks by city for chassis
    const chassisRacksByCity = ' . json_encode($racksByCity) . ';

    // Initialize chassis count and button state
    function updateChassisCount() {
        let count = 0;
        const chassisLabelInputs = document.querySelectorAll(".chassis-label-input");

        chassisLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                count++;
            }
        });

        // Update counter display
        const counter = document.getElementById("chassisCount");
        if (counter) {
            counter.textContent = count;
        }

        // Update submit button
        const submitBtn = document.querySelector("button[type=submit]");
        if (submitBtn) {
            if (count > 0) {
                submitBtn.disabled = false;
                submitBtn.style.opacity = "1";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add " + count + " Chassis";
            } else {
                submitBtn.disabled = true;
                submitBtn.style.opacity = "0.6";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add 0 Chassis";
            }
        }

        console.log("Chassis count updated to:", count);
    }

    // Handle AJAX form submission for chassis
    function handleChassisAjaxSubmit(form, event) {
        console.log("handleChassisAjaxSubmit called, isChassisAjaxModal:", isChassisAjaxModal);

        if (!isChassisAjaxModal) {
            return true; // Allow normal submission
        }

        // Prevent normal form submission
        if (event) {
            event.preventDefault();
        }

        console.log("Preventing default submission, starting chassis AJAX submission");

        // Validate form before submission
        let hasValidChassis = false;
        let filledCount = 0;

        const chassisLabelInputs = document.querySelectorAll(".chassis-label-input");
        chassisLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                hasValidChassis = true;
                filledCount++;
            }
        });

        if (!hasValidChassis) {
            alert("Please fill in at least one chassis label before submitting.");
            return false;
        }

        console.log("Chassis form validation passed, " + filledCount + " chassis to add");

        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding " + filledCount + " Chassis...";
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);

        console.log("Chassis form data being submitted");

        // Submit via AJAX
        const ajaxUrl = "' . $modulelink . '&action=chassies_manage&ajax=1";
        console.log("Chassis AJAX URL:", ajaxUrl);

        fetch(ajaxUrl, {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            console.log("Chassis AJAX response received");

            const isSuccess = html.includes("chassis(es) added successfully") ||
                             html.includes("ajaxSuccessIndicator") ||
                             html.includes("SUCCESS:");

            if (isSuccess) {
                console.log("Chassis success detected in response");
                const modalContainer = document.getElementById("addChassisModalContainer");
                modalContainer.innerHTML = html;

                setTimeout(function() {
                    closeModal();
                    window.location.reload();
                }, 2000);
            } else {
                console.log("Chassis submission completed but no clear success indicator");
                const modalContainer = document.getElementById("addChassisModalContainer");
                if (modalContainer) {
                    modalContainer.innerHTML = html;

                    const scripts = modalContainer.querySelectorAll("script");
                    scripts.forEach(script => {
                        try {
                            eval(script.textContent);
                        } catch (e) {
                            console.error("Error executing script:", e);
                        }
                    });

                    if (typeof initializeChassisAjaxModal === "function") {
                        initializeChassisAjaxModal();
                    }
                }
            }
        })
        .catch(error => {
            console.error("Chassis AJAX submission error:", error);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            alert("Error submitting form. Please try again.");
        });

        return false;
    }

    // Chassis-specific functions
    function addChassisRow() {
        console.log("addChassisRow called");
        // Implementation would add dynamic rows
        updateChassisCount();
    }

    function addFiveChassisRows() {
        console.log("addFiveChassisRows called");
        // Implementation would add 5 dynamic rows
        updateChassisCount();
    }

    function removeChassisRow(button) {
        console.log("removeChassisRow called");
        const row = button.closest("tr");
        if (row) {
            row.remove();
            updateChassisCount();
        }
    }

    function updateChassisRackOptions(chassisIndex) {
        const citySelect = document.querySelector("select[name=\"chassies[" + chassisIndex + "][city]\"]");
        const rackSelect = document.querySelector("select[name=\"chassies[" + chassisIndex + "][rack_id]\"]");

        if (!citySelect || !rackSelect) return;

        const selectedCity = citySelect.value;
        rackSelect.innerHTML = "<option value=\"\">Select Rack</option>";

        if (selectedCity && chassisRacksByCity[selectedCity]) {
            chassisRacksByCity[selectedCity].forEach(function(rack) {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.name + " (" + rack.units + " units)";
                rackSelect.appendChild(option);
            });
        }
    }

    function syncChassisLabels() {
        const chassisLabelInputs = document.querySelectorAll(".chassis-label-input");
        const chassisLabelDisplays = document.querySelectorAll(".chassis-label-display");

        chassisLabelInputs.forEach(function(input, index) {
            if (chassisLabelDisplays[index]) {
                const label = input.value.trim() || ("Chassis " + (index + 1));
                chassisLabelDisplays[index].textContent = label;
            }
        });
    }

    // Attach listeners to inputs for real-time updates
    function attachChassisLabelListeners() {
        document.querySelectorAll(".chassis-label-input").forEach(function(input) {
            input.addEventListener("input", function() {
                updateChassisCount();
                syncChassisLabels();
            });
        });
    }

    // Tab functionality
    function initChassisTabsAndListeners() {
        var tabLinks = document.querySelectorAll(".nav-tabs a");
        var tabPanes = document.querySelectorAll(".tab-pane");

        tabLinks.forEach(function(link) {
            link.addEventListener("click", function(e) {
                e.preventDefault();
                tabLinks.forEach(function(l) { l.parentElement.classList.remove("active"); });
                tabPanes.forEach(function(p) { p.classList.remove("active"); });
                this.parentElement.classList.add("active");
                var target = this.getAttribute("href").replace("#", "");
                var pane = document.getElementById(target);
                if (pane) pane.classList.add("active");
            });
        });

        if (tabLinks.length > 0) {
            tabLinks[0].click();
        }

        // Attach chassis label listeners
        attachChassisLabelListeners();

        // Initial count update
        updateChassisCount();
        syncChassisLabels();
    }

    // Setup chassis model form submission
    document.addEventListener("DOMContentLoaded", function() {
        const chassisModelForm = document.getElementById("addChassisModelForm");
        if (chassisModelForm) {
            chassisModelForm.addEventListener("submit", function(e) {
                e.preventDefault();
                const formData = {
                    ajax_action: "add_chassis_model",
                    name: document.getElementById("chassisModelName").value,
                    manufacturer: document.getElementById("chassisModelManufacturer").value,
                    description: document.getElementById("chassisModelDescription").value
                };
                addChassisModel(formData);
            });
        }

        // Initialize tabs and listeners
        initChassisTabsAndListeners();
    });

    // Make functions globally available
    window.handleChassisAjaxSubmit = handleChassisAjaxSubmit;
    window.addChassisRow = addChassisRow;
    window.addFiveChassisRows = addFiveChassisRows;
    window.removeChassisRow = removeChassisRow;
    window.updateChassisRackOptions = updateChassisRackOptions;
    window.updateChassisCount = updateChassisCount;
    window.syncChassisLabels = syncChassisLabels;
    window.attachChassisLabelListeners = attachChassisLabelListeners;
    window.initChassisTabsAndListeners = initChassisTabsAndListeners;
    </script>';
}



/**
 * Generate JavaScript for chassis management functionality
 * 
 * @param string $modulelink The WHMCS module link
 */
function dcim_generate_chassis_javascript($modulelink) {
    echo '<script>
    function editChassis(chassisId) {
        window.location.href = "' . $modulelink . '&action=chassies&edit=" + chassisId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddChassisModal() {
        // Show loading state
        const modalContainer = document.getElementById("addChassisModalContainer");
        modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"font-size: 24px; color: #4299e1; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #6b7280;\">Loading chassis configuration...</p></div></div>";
        modalContainer.style.display = "block";

        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=chassies_manage&ajax=1")
            .then(response => response.text())
            .then(html => {
                modalContainer.innerHTML = html;

                // Execute any scripts in the loaded content
                const scripts = modalContainer.querySelectorAll("script");
                scripts.forEach(script => {
                    try {
                        eval(script.textContent);
                    } catch (e) {
                        console.error("Error executing script:", e);
                    }
                });

                // Initialize modal content after scripts are loaded
                setTimeout(function() {
                    try {
                        // Force initialization of all modal functions
                        if (typeof initializeChassisAjaxModal === "function") {
                            initializeChassisAjaxModal();
                        }
                    } catch (error) {
                        console.log("Could not initialize chassis modal content:", error);
                    }
                }, 200);
            })
            .catch(error => {
                console.error("Error loading chassis interface:", error);
                modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-exclamation-triangle\" style=\"font-size: 24px; color: #ef4444; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #374151;\">Error loading chassis configuration</p><button onclick=\"closeModal()\" style=\"margin-top: 16px; background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;\">Close</button></div></div>";
            });
    }
    
    function closeModal() {
        const modalContainer = document.getElementById("addChassisModalContainer");
        modalContainer.style.display = "none";
        modalContainer.innerHTML = "";
    }
    
    function goBack() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    </script>';
}

/**
 * Get chassis statistics for dashboard
 * 
 * @return array Chassis statistics
 */
function dcim_get_chassis_stats() {
    try {
        $total_chassis = Capsule::table('dcim_chassies')->count();
        $online_chassis = Capsule::table('dcim_chassies')->where('status', 'online')->count();
        $assigned_chassis = Capsule::table('dcim_chassies')->whereNotNull('rack_id')->count();
        $unassigned_chassis = Capsule::table('dcim_chassies')->whereNull('rack_id')->count();
        
        $blade_chassis = Capsule::table('dcim_chassies')->where('chassis_type', 'blade')->count();
        $storage_chassis = Capsule::table('dcim_chassies')->where('chassis_type', 'storage')->count();
        $network_chassis = Capsule::table('dcim_chassies')->where('chassis_type', 'network')->count();
        
        $total_slots = Capsule::table('dcim_chassies')->sum('slots');
        
        return [
            'total' => $total_chassis,
            'online' => $online_chassis,
            'assigned' => $assigned_chassis,
            'unassigned' => $unassigned_chassis,
            'blade' => $blade_chassis,
            'storage' => $storage_chassis,
            'network' => $network_chassis,
            'total_slots' => $total_slots,
            'online_percentage' => $total_chassis > 0 ? round(($online_chassis / $total_chassis) * 100, 1) : 0,
            'assignment_percentage' => $total_chassis > 0 ? round(($assigned_chassis / $total_chassis) * 100, 1) : 0
        ];
    } catch (Exception $e) {
        error_log("DCIM: Error getting chassis stats - " . $e->getMessage());
        return [
            'total' => 0,
            'online' => 0,
            'assigned' => 0,
            'unassigned' => 0,
            'blade' => 0,
            'storage' => 0,
            'network' => 0,
            'total_slots' => 0,
            'online_percentage' => 0,
            'assignment_percentage' => 0
        ];
    }
}

/**
 * Get all chassis for a specific rack
 * 
 * @param int $rack_id The rack ID
 * @return array Array of chassis objects
 */
function dcim_get_rack_chassis($rack_id) {
    try {
        return Capsule::table('dcim_chassies')
            ->where('rack_id', $rack_id)
            ->orderBy('start_unit', 'desc')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching rack chassis - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Validate chassis data
 * 
 * @param array $data Chassis data to validate
 * @return array Array with 'valid' boolean and 'errors' array
 */
function dcim_validate_chassis_data($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors[] = 'Chassis name is required';
    }
    
    if (!empty($data['start_unit']) && (!is_numeric($data['start_unit']) || $data['start_unit'] < 1 || $data['start_unit'] > 42)) {
        $errors[] = 'Start unit must be between 1 and 42';
    }
    
    if (!empty($data['unit_size']) && (!is_numeric($data['unit_size']) || $data['unit_size'] < 1 || $data['unit_size'] > 20)) {
        $errors[] = 'Unit size must be between 1 and 20';
    }
    
    if (!empty($data['slots']) && (!is_numeric($data['slots']) || $data['slots'] < 1 || $data['slots'] > 32)) {
        $errors[] = 'Slots must be between 1 and 32';
    }
    
    if (!empty($data['management_ip']) && !filter_var($data['management_ip'], FILTER_VALIDATE_IP)) {
        $errors[] = 'Invalid management IP address format';
    }
    
    if (!empty($data['chassis_type']) && !in_array($data['chassis_type'], ['blade', 'storage', 'network'])) {
        $errors[] = 'Invalid chassis type';
    }
    
    return [
        'valid' => count($errors) == 0,
        'errors' => $errors
    ];
}

/**
 * Create a new chassis
 * 
 * @param array $data Chassis data
 * @return array Result array with success/error information
 */
function dcim_create_chassis($data) {
    try {
        $validation = dcim_validate_chassis_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $chassis_id = Capsule::table('dcim_chassies')->insertGetId([
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 10,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'slots' => $data['slots'] ?? 8,
            'chassis_type' => $data['chassis_type'] ?? 'blade',
            'management_ip' => $data['management_ip'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'offline',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        return ['success' => true, 'chassis_id' => $chassis_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating chassis - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update an existing chassis
 * 
 * @param int $chassis_id The chassis ID
 * @param array $data Updated chassis data
 * @return array Result array with success/error information
 */
function dcim_update_chassis($chassis_id, $data) {
    try {
        $validation = dcim_validate_chassis_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $updateData = [
            'name' => $data['name'],
            'hostname' => $data['hostname'] ?? null,
            'rack_id' => $data['rack_id'] ?? null,
            'start_unit' => $data['start_unit'] ?? null,
            'unit_size' => $data['unit_size'] ?? 10,
            'make' => $data['make'] ?? null,
            'model' => $data['model'] ?? null,
            'serial_number' => $data['serial_number'] ?? null,
            'slots' => $data['slots'] ?? 8,
            'chassis_type' => $data['chassis_type'] ?? 'blade',
            'management_ip' => $data['management_ip'] ?? null,
            'power_consumption' => $data['power_consumption'] ?? 0,
            'client_id' => $data['client_id'] ?? null,
            'service_id' => $data['service_id'] ?? null,
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        Capsule::table('dcim_chassies')
            ->where('id', $chassis_id)
            ->update($updateData);
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error updating chassis - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Delete a chassis
 * 
 * @param int $chassis_id The chassis ID to delete
 * @return array Result array with success/error information
 */
function dcim_delete_chassis($chassis_id) {
    try {
        // Also clean up any IP assignments
        $ip_assignments = Capsule::table('dcim_ip_assignments')
            ->where('device_type', 'chassis')
            ->where('device_id', $chassis_id)
            ->get();
            
        foreach ($ip_assignments as $assignment) {
            // Mark IP as available again
            Capsule::table('dcim_ip_addresses')
                ->where('id', $assignment->ip_address_id)
                ->update(['status' => 'available', 'updated_at' => date('Y-m-d H:i:s')]);
                
            // Delete assignment
            Capsule::table('dcim_ip_assignments')->where('id', $assignment->id)->delete();
        }
        
        // Delete the chassis
        Capsule::table('dcim_chassies')->where('id', $chassis_id)->delete();
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting chassis - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get all chassis types
 * 
 * @return array Array of chassis types
 */
function dcim_get_chassis_types() {
    return [
        'blade' => 'Blade Chassis',
        'storage' => 'Storage Chassis', 
        'network' => 'Network Chassis'
    ];
}

/**
 * Generate JavaScript for chassis management functionality
 *
 * @param string $modulelink The WHMCS module link
 */
function dcim_generate_chassis_javascript($modulelink) {
    echo '<script>
    function editChassis(chassisId) {
        window.location.href = "' . $modulelink . '&action=chassies&edit=" + chassisId;
    }

    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }

    function showAddChassisModal() {
        // Show loading state
        const modalContainer = document.getElementById("addChassisModalContainer");
        modalContainer.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">
                <div style="background: white; border-radius: 12px; padding: 40px; text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #4299e1; margin-bottom: 16px;"></i>
                    <p style="margin: 0; color: #6b7280;">Loading chassis configuration...</p>
                </div>
            </div>
        `;
        modalContainer.style.display = "block";

        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=chassies_manage&ajax=1")
            .then(response => response.text())
            .then(html => {
                modalContainer.innerHTML = html;

                // Execute any scripts in the loaded content
                const scripts = modalContainer.querySelectorAll("script");
                scripts.forEach(script => {
                    try {
                        eval(script.textContent);
                    } catch (e) {
                        console.error("Error executing script:", e);
                    }
                });

                // Initialize modal content after scripts are loaded
                setTimeout(function() {
                    try {
                        // Force initialization of all modal functions
                        if (typeof initializeChassisAjaxModal === "function") {
                            initializeChassisAjaxModal();
                        }
                    } catch (error) {
                        console.log("Could not initialize chassis modal content:", error);
                    }
                }, 200);
            })
            .catch(error => {
                console.error("Error loading chassis interface:", error);
                modalContainer.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">
                        <div style="background: white; border-radius: 12px; padding: 40px; text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #ef4444; margin-bottom: 16px;"></i>
                            <p style="margin: 0; color: #374151;">Error loading chassis configuration</p>
                            <button onclick="closeModal()" style="margin-top: 16px; background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">Close</button>
                        </div>
                    </div>
                `;
            });
    }

    function closeModal() {
        const modalContainer = document.getElementById("addChassisModalContainer");
        if (modalContainer) {
            modalContainer.style.display = "none";
            modalContainer.innerHTML = "";
        }
    }

    // Make functions globally available
    window.editChassis = editChassis;
    window.viewInRack = viewInRack;
    window.showAddChassisModal = showAddChassisModal;
    window.closeModal = closeModal;
    </script>';
}

/**
 * Chassis management functions
 * Note: dcim_get_chassis_models() is defined in dcim-core.php to avoid conflicts
 */

?>